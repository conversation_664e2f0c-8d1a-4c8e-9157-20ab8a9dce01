/**
 * 🤖 AI Testing Toolkit - Basic Usage Examples
 * 
 * This file demonstrates how AI agents should use the testing toolkit
 * for common testing scenarios with Electron + React applications.
 */

const { AITestingToolkit } = require('../run-all-tests');
const { ElectronTester } = require('../core-tools/electron-tester');
const { <PERSON><PERSON>er<PERSON>ontroller } = require('../core-tools/browser-controller');
const { IPCTester } = require('../ipc-tools/ipc-tester');
const { WorkflowTester } = require('../integration-tools/workflow-tester');

/**
 * Example 1: Quick Application Health Check
 */
async function quickHealthCheck(appPath) {
    console.log('🏥 Running quick health check...');
    
    const electronTester = new ElectronTester(appPath);
    
    try {
        // Start the application
        await electronTester.start();
        
        // Check if it's running
        const isHealthy = electronTester.isHealthy();
        console.log(`Application health: ${isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
        
        // Get basic metrics
        const metrics = electronTester.getMetrics();
        console.log('Metrics:', metrics);
        
        return isHealthy;
        
    } finally {
        await electronTester.stop();
    }
}

/**
 * Example 2: Test File Analysis Workflow
 */
async function testFileAnalysis(appPath, testFile) {
    console.log('🎵 Testing file analysis workflow...');
    
    const workflowTester = new WorkflowTester(appPath);
    
    try {
        // Test complete file analysis workflow
        const result = await workflowTester.testFileAnalysisWorkflow([testFile]);
        
        console.log(`Workflow result: ${result.success ? '✅ Success' : '❌ Failed'}`);
        
        if (!result.success) {
            console.log('Errors:', result.errors);
            result.steps.forEach(step => {
                if (!step.success) {
                    console.log(`Failed step: ${step.name} - ${step.error}`);
                }
            });
        }
        
        return result;
        
    } finally {
        await workflowTester.cleanup();
    }
}

/**
 * Example 3: Debug IPC Communication Issues
 */
async function debugIPCIssues(appPath) {
    console.log('🔧 Debugging IPC communication...');
    
    const ipcTester = new IPCTester(appPath);
    
    try {
        // Check if all required handlers exist
        const requiredHandlers = [
            'analyze-file',
            'open-file-dialog',
            'get-settings'
        ];
        
        const handlerValidation = await ipcTester.validateHandlers(requiredHandlers);
        console.log('Handler validation:', handlerValidation);
        
        // Test a specific handler
        if (handlerValidation.available.includes('analyze-file')) {
            const testFile = './test-files/sample.wav';
            const handlerResult = await ipcTester.testHandler('analyze-file', testFile);
            console.log('Handler test result:', handlerResult);
        }
        
        // Test event flow
        const eventFlow = await ipcTester.testIPCFlow(
            'analyze-file',
            ['./test-files/sample.wav', { calculateWaveform: true }],
            ['analysis-progress', 'analysis-complete']
        );
        console.log('Event flow test:', eventFlow);
        
        return {
            handlers: handlerValidation,
            eventFlow: eventFlow
        };
        
    } catch (error) {
        console.error('IPC debugging failed:', error);
        return { error: error.message };
    }
}

/**
 * Example 4: Test UI Interactions
 */
async function testUIInteractions(appPath) {
    console.log('🎨 Testing UI interactions...');
    
    const browser = new BrowserController({ headless: false });
    
    try {
        // Navigate to the application
        await browser.navigate('http://localhost:3000');
        
        // Wait for the app to load
        await browser.waitForElement('body');
        
        // Check if key elements exist
        const hasDropZone = await browser.elementExists('[data-testid="drop-zone"]');
        const hasFileList = await browser.elementExists('[data-testid="file-list"]');
        const hasAnalysisPanel = await browser.elementExists('[data-testid="analysis-panel"]');
        
        console.log('UI Elements:');
        console.log(`  Drop Zone: ${hasDropZone ? '✅' : '❌'}`);
        console.log(`  File List: ${hasFileList ? '✅' : '❌'}`);
        console.log(`  Analysis Panel: ${hasAnalysisPanel ? '✅' : '❌'}`);
        
        // Test file upload if drop zone exists
        if (hasDropZone) {
            const testFiles = ['./test-files/sample.wav'];
            // Note: In real usage, you'd use FileDropSimulator here
            console.log('Would test file drop with:', testFiles);
        }
        
        // Get console logs for debugging
        const logs = browser.getConsoleLogs();
        const errors = logs.filter(log => log.type === 'error');
        
        if (errors.length > 0) {
            console.log('Console errors found:');
            errors.forEach(error => console.log(`  ${error.text}`));
        }
        
        return {
            elements: { hasDropZone, hasFileList, hasAnalysisPanel },
            errors: errors
        };
        
    } finally {
        await browser.close();
    }
}

/**
 * Example 5: Complete Application Test
 */
async function completeApplicationTest(appPath) {
    console.log('🚀 Running complete application test...');
    
    const toolkit = new AITestingToolkit(appPath, {
        debug: true,
        headless: false,
        generateTestFiles: true
    });
    
    // Run the full test suite
    const results = await toolkit.runAllTests();
    
    console.log('\n📊 Complete Test Results:');
    console.log(`Success Rate: ${results.summary.testSuccessRate}%`);
    console.log(`Total Duration: ${(results.totalDuration / 1000).toFixed(1)}s`);
    
    // Show failed tests
    const failedTests = results.tests.filter(test => !test.success);
    if (failedTests.length > 0) {
        console.log('\n❌ Failed Tests:');
        failedTests.forEach(test => {
            console.log(`  ${test.name}: ${test.error || 'Unknown error'}`);
        });
    }
    
    return results;
}

/**
 * Example 6: Custom Test Scenario
 */
async function customTestScenario(appPath) {
    console.log('🎯 Running custom test scenario...');
    
    // This example shows how to combine multiple tools for a specific test
    const electronTester = new ElectronTester(appPath);
    const browser = new BrowserController();
    
    try {
        // Step 1: Start application
        console.log('Step 1: Starting application...');
        await electronTester.start();
        
        // Step 2: Load UI
        console.log('Step 2: Loading UI...');
        await browser.navigate('http://localhost:3000');
        
        // Step 3: Wait for specific condition
        console.log('Step 3: Waiting for app ready state...');
        await browser.waitForText('AlbumPlayer'); // Wait for app title
        
        // Step 4: Perform specific test
        console.log('Step 4: Testing specific functionality...');
        const hasTitle = await browser.elementExists('h1');
        
        // Step 5: Validate results
        console.log('Step 5: Validating results...');
        const success = hasTitle;
        
        console.log(`Custom test result: ${success ? '✅ Success' : '❌ Failed'}`);
        
        return { success, hasTitle };
        
    } finally {
        await browser.close();
        await electronTester.stop();
    }
}

// Export examples for use by AI agents
module.exports = {
    quickHealthCheck,
    testFileAnalysis,
    debugIPCIssues,
    testUIInteractions,
    completeApplicationTest,
    customTestScenario
};

// CLI usage examples
if (require.main === module) {
    const appPath = process.argv[2] || process.cwd();
    const example = process.argv[3] || 'health';
    
    console.log(`🤖 Running example: ${example}`);
    console.log(`📁 App path: ${appPath}\n`);
    
    switch (example) {
        case 'health':
            quickHealthCheck(appPath);
            break;
        case 'analysis':
            testFileAnalysis(appPath, './test-files/sample.wav');
            break;
        case 'ipc':
            debugIPCIssues(appPath);
            break;
        case 'ui':
            testUIInteractions(appPath);
            break;
        case 'complete':
            completeApplicationTest(appPath);
            break;
        case 'custom':
            customTestScenario(appPath);
            break;
        default:
            console.log('Available examples: health, analysis, ipc, ui, complete, custom');
    }
}
