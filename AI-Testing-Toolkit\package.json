{"name": "ai-testing-toolkit", "version": "1.0.0", "description": "Comprehensive testing toolkit for AI agents working with Electron + React applications", "main": "index.js", "scripts": {"test": "node run-all-tests.js", "test-core": "node tests/test-core-tools.js", "test-ipc": "node tests/test-ipc-tools.js", "test-audio": "node tests/test-audio-tools.js", "test-ui": "node tests/test-ui-tools.js", "test-integration": "node tests/test-integration-tools.js", "install-deps": "npm install", "setup": "node setup.js"}, "keywords": ["ai", "testing", "electron", "react", "audio", "automation", "playwright", "ipc"], "author": "AI Testing Toolkit", "license": "MIT", "dependencies": {"playwright": "^1.40.0", "electron": "^25.0.0"}, "devDependencies": {"jest": "^29.0.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/ai-testing-toolkit/ai-testing-toolkit.git"}, "bugs": {"url": "https://github.com/ai-testing-toolkit/ai-testing-toolkit/issues"}, "homepage": "https://github.com/ai-testing-toolkit/ai-testing-toolkit#readme"}