# 🤖 AI Agent Testing Toolkit for Electron + React Applications

## 📋 Overview

This toolkit provides AI agents with comprehensive testing capabilities for Electron + React applications, particularly those involving audio processing, file handling, and complex IPC communication.

## 🎯 Purpose

- **Immediate Productivity**: Drop this folder into any project for instant testing capabilities
- **Comprehensive Coverage**: Tools for UI, backend, IPC, audio processing, and integration testing
- **AI-Optimized**: Designed specifically for AI agents to use programmatically
- **Reusable**: Works across different Electron + React projects with minimal configuration

## 📁 Toolkit Structure

```
AI-Testing-Toolkit/
├── README.md                          # This guide
├── core-tools/                        # Essential testing tools
├── audio-tools/                       # Audio-specific testing utilities
├── ipc-tools/                         # IPC communication testing
├── ui-tools/                          # UI interaction and validation
├── performance-tools/                 # Performance and monitoring
├── integration-tools/                 # End-to-end testing
├── config/                           # Configuration templates
└── examples/                         # Usage examples
```

## 🚀 Quick Start for AI Agents

### 1. Setup
```bash
# Copy this folder to your project root
cp -r AI-Testing-Toolkit /path/to/your/project/

# Install dependencies
cd AI-Testing-Toolkit
npm install
```

### 2. Basic Usage
```javascript
const { ElectronTester } = require('./AI-Testing-Toolkit/core-tools/electron-tester');
const tester = new ElectronTester('/path/to/your/electron/app');
await tester.start();
```

### 3. Run Test Suite
```bash
node AI-Testing-Toolkit/run-all-tests.js
```

## 🔧 Core Tools Reference

### **Core Development Tools**

#### `electron-tester.js`
**Purpose**: Main Electron application testing controller
**Usage**:
```javascript
const tester = new ElectronTester(appPath);
await tester.start();
await tester.loadApp();
const result = await tester.testIPC('analyze-file', filePath);
```

#### `browser-controller.js`
**Purpose**: Playwright-based browser automation
**Usage**:
```javascript
const browser = new BrowserController();
await browser.navigate('http://localhost:3000');
await browser.clickElement('#upload-button');
const logs = await browser.getConsoleLogs();
```

#### `ipc-tester.js`
**Purpose**: Direct IPC communication testing
**Usage**:
```javascript
const ipc = new IPCTester();
await ipc.testHandler('analyze-file', testData);
const events = await ipc.monitorEvents(['analysis-complete']);
```

### **Audio Processing Tools**

#### `audio-file-generator.js`
**Purpose**: Generate test audio files with known properties
**Usage**:
```javascript
const generator = new AudioFileGenerator();
await generator.createTestFile({
  duration: 10,
  sampleRate: 48000,
  lufs: -14.0,
  format: 'wav'
});
```

#### `lufs-validator.js`
**Purpose**: Independent LUFS calculation for verification
**Usage**:
```javascript
const validator = new LUFSValidator();
const result = await validator.calculateLUFS(filePath);
assert.closeTo(result.integrated, expectedLUFS, 0.1);
```

### **UI Testing Tools**

#### `file-drop-simulator.js`
**Purpose**: Simulate drag & drop file operations
**Usage**:
```javascript
const simulator = new FileDropSimulator();
await simulator.dropFiles(['test1.wav', 'test2.mp3'], '#drop-zone');
```

#### `waveform-tester.js`
**Purpose**: Test waveform visualization and interaction
**Usage**:
```javascript
const waveform = new WaveformTester();
await waveform.testRendering(waveformData);
await waveform.testSeeking(0.5); // Seek to 50%
```

## 📊 Testing Workflows

### **Complete Application Test**
```javascript
async function testCompleteWorkflow() {
  // 1. Start application
  const app = new ElectronTester('./');
  await app.start();
  
  // 2. Test file loading
  const fileDropper = new FileDropSimulator();
  await fileDropper.dropFiles(['test.wav']);
  
  // 3. Monitor analysis
  const ipc = new IPCTester();
  const result = await ipc.waitForEvent('analysis-complete', 30000);
  
  // 4. Validate results
  const validator = new LUFSValidator();
  await validator.verifyResult(result);
  
  // 5. Test UI updates
  const browser = new BrowserController();
  const analysisPanel = await browser.getElement('#analysis-panel');
  assert.isTrue(analysisPanel.isVisible);
}
```

### **IPC Communication Test**
```javascript
async function testIPCCommunication() {
  const ipc = new IPCTester();
  
  // Test all handlers exist
  await ipc.verifyHandlerExists('analyze-file');
  await ipc.verifyHandlerExists('open-file-dialog');
  
  // Test event flow
  const events = await ipc.monitorEvents([
    'analysis-progress',
    'analysis-complete',
    'error'
  ]);
  
  // Trigger analysis
  await ipc.callHandler('analyze-file', 'test.wav');
  
  // Verify events received
  assert.isTrue(events.received('analysis-complete'));
}
```

## 🎯 AI Agent Usage Patterns

### **Debugging Workflow**
1. Use `electron-tester.js` to start the app
2. Use `ipc-tester.js` to verify backend functionality
3. Use `browser-controller.js` to check frontend state
4. Use `log-aggregator.js` to collect all logs
5. Use `error-analyzer.js` to identify issues

### **Performance Testing**
1. Use `memory-monitor.js` to track RAM usage
2. Use `timing-profiler.js` to measure analysis speed
3. Use `stress-tester.js` for load testing
4. Use `resource-monitor.js` for cleanup verification

### **Regression Testing**
1. Use `snapshot-comparer.js` for UI regression
2. Use `result-validator.js` for output verification
3. Use `benchmark-runner.js` for performance regression

## 🔍 Troubleshooting Guide

### **Common Issues**

#### "Analysis stuck on 'Analyzing...'"
```javascript
// Use this diagnostic sequence
const diagnostics = new DiagnosticRunner();
await diagnostics.checkIPCCommunication();
await diagnostics.checkEventListeners();
await diagnostics.checkBackendAnalysis();
```

#### "Files not loading"
```javascript
// Test file handling pipeline
const fileHandler = new FileHandlerTester();
await fileHandler.testFileDialog();
await fileHandler.testDragDrop();
await fileHandler.testFileValidation();
```

## 📈 Advanced Features

### **Custom Test Scenarios**
Create custom test scenarios in `scenarios/` folder:
```javascript
// scenarios/spotify-normalization-test.js
module.exports = async function(toolkit) {
  const { audioGenerator, lufsValidator, browser } = toolkit;
  
  // Generate test file with specific LUFS
  const testFile = await audioGenerator.create({ lufs: -20 });
  
  // Test normalization calculation
  await browser.dropFile(testFile);
  const result = await browser.waitForAnalysis();
  
  // Verify Spotify normalization
  assert.equal(result.gainApplied, 6.0); // -20 to -14 LUFS
};
```

### **Continuous Integration**
```yaml
# .github/workflows/ai-testing.yml
- name: Run AI Testing Toolkit
  run: |
    cd AI-Testing-Toolkit
    npm test
```

## 🎵 Audio-Specific Testing

### **LUFS Testing**
- Generate files with known LUFS values
- Test normalization calculations
- Verify Spotify compliance (-14 LUFS target)

### **True Peak Testing**
- Test peak detection accuracy
- Verify ceiling compliance (-1 dBTP)
- Test intersample peak detection

### **Format Support**
- Test WAV, FLAC, MP3, AAC support
- Verify metadata extraction
- Test error handling for unsupported formats

## 🔧 Configuration

### **Project-Specific Setup**
Edit `config/project-config.js`:
```javascript
module.exports = {
  electronPath: './main/main.js',
  reactUrl: 'http://localhost:3000',
  testFilesPath: './test-files/',
  outputPath: './test-results/',
  timeouts: {
    analysis: 30000,
    startup: 10000,
    ui: 5000
  }
};
```

## 📝 Contributing

When adding new tools:
1. Follow the naming convention: `tool-name.js`
2. Include JSDoc documentation
3. Add usage examples
4. Update this README
5. Add tests in `tests/` folder

## 🎯 Future Enhancements

- Visual regression testing
- Cross-platform testing automation
- Performance benchmarking database
- AI-powered test case generation
- Automated bug report generation

## 🚀 Quick Start Commands

```bash
# Copy toolkit to your project
cp -r AI-Testing-Toolkit /path/to/your/project/

# Install dependencies
cd AI-Testing-Toolkit && npm install

# Run complete test suite
npm test

# Run specific test categories
npm run test-core      # Core tools only
npm run test-ipc       # IPC communication only
npm run test-audio     # Audio processing only
npm run test-ui        # UI interaction only
npm run test-integration # End-to-end workflows

# Run examples
node examples/basic-usage.js /path/to/app health
node examples/basic-usage.js /path/to/app complete
```

## 📱 Integration with Existing Projects

Add to your project's package.json:
```json
{
  "scripts": {
    "test:ai": "node AI-Testing-Toolkit/run-all-tests.js",
    "test:health": "node AI-Testing-Toolkit/examples/basic-usage.js . health"
  }
}
```

---

**🤖 For AI Agents**: This toolkit is designed to be your comprehensive testing companion. Start with the core tools, then use specialized tools based on your testing needs. Always check the logs and use multiple validation methods for reliable results.

**🎯 Recommended Workflow for AI Agents**:
1. Start with `quickHealthCheck()` to verify app basics
2. Use `debugIPCIssues()` if communication problems exist
3. Run `testFileAnalysis()` for audio-specific functionality
4. Use `completeApplicationTest()` for comprehensive validation
5. Create custom scenarios for specific requirements
