/**
 * 🤖 AI Agent IPC Communication Tester
 * 
 * Comprehensive testing tool for Electron IPC communication.
 * Tests handlers, monitors events, and validates data flow
 * between main and renderer processes.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;

class IPCTester {
    constructor(appPath, options = {}) {
        this.appPath = appPath;
        this.options = {
            timeout: 30000,
            debug: true,
            ...options
        };
        
        this.events = [];
        this.handlers = new Map();
    }

    /**
     * Test if IPC handler exists and is callable
     */
    async testHandler(handlerName, ...args) {
        console.log(`🔧 Testing IPC handler: ${handlerName}`);
        
        const testScript = this.generateHandlerTestScript(handlerName, args);
        const result = await this.executeTestScript(testScript);
        
        if (result.error) {
            throw new Error(`Handler test failed: ${result.error}`);
        }
        
        console.log(`✅ Handler ${handlerName} test completed`);
        return result;
    }

    /**
     * Monitor IPC events for a specific duration
     */
    async monitorEvents(eventNames, duration = 10000) {
        console.log(`👂 Monitoring IPC events: ${eventNames.join(', ')}`);
        
        const monitorScript = this.generateEventMonitorScript(eventNames, duration);
        const result = await this.executeTestScript(monitorScript);
        
        console.log(`📊 Captured ${result.events.length} events`);
        return result.events;
    }

    /**
     * Test complete IPC flow (call handler and monitor events)
     */
    async testIPCFlow(handlerName, args, expectedEvents, timeout = 30000) {
        console.log(`🔄 Testing complete IPC flow for: ${handlerName}`);
        
        const flowScript = this.generateFlowTestScript(handlerName, args, expectedEvents, timeout);
        const result = await this.executeTestScript(flowScript);
        
        if (result.error) {
            throw new Error(`IPC flow test failed: ${result.error}`);
        }
        
        console.log(`✅ IPC flow test completed successfully`);
        return result;
    }

    /**
     * Validate all required handlers exist
     */
    async validateHandlers(requiredHandlers) {
        console.log('🔍 Validating required IPC handlers...');
        
        const validationScript = this.generateHandlerValidationScript(requiredHandlers);
        const result = await this.executeTestScript(validationScript);
        
        const missing = result.missing || [];
        if (missing.length > 0) {
            throw new Error(`Missing IPC handlers: ${missing.join(', ')}`);
        }
        
        console.log('✅ All required handlers are available');
        return result.available;
    }

    /**
     * Test analysis workflow specifically
     */
    async testAnalysisWorkflow(filePath) {
        console.log('🎵 Testing analysis workflow...');
        
        const workflowScript = this.generateAnalysisWorkflowScript(filePath);
        const result = await this.executeTestScript(workflowScript);
        
        if (result.error) {
            throw new Error(`Analysis workflow failed: ${result.error}`);
        }
        
        console.log('✅ Analysis workflow completed successfully');
        return result;
    }

    /**
     * Generate test script for handler testing
     */
    generateHandlerTestScript(handlerName, args) {
        return `
const { ipcMain } = require('electron');

// Load all IPC handlers
try {
    require('./main/ipc/ipcHandlers');
    require('./main/ipc/fileHandlers');
    require('./main/ipc/analysisHandlers');
} catch (error) {
    console.log('RESULT:', JSON.stringify({ error: 'Failed to load handlers: ' + error.message }));
    process.exit(1);
}

// Mock event object
const mockEvent = {
    sender: {
        send: (channel, data) => {
            console.log('EVENT:', JSON.stringify({ channel, data }));
        }
    }
};

async function testHandler() {
    try {
        // Get handler
        const handlers = ipcMain._events || {};
        const handler = handlers['${handlerName}'];
        
        if (!handler) {
            throw new Error('Handler not found');
        }
        
        // Call handler
        const result = await handler[0](mockEvent, ${JSON.stringify(args).slice(1, -1)});
        
        console.log('RESULT:', JSON.stringify({ 
            success: true, 
            result: result,
            handlerName: '${handlerName}'
        }));
        
    } catch (error) {
        console.log('RESULT:', JSON.stringify({ 
            error: error.message,
            handlerName: '${handlerName}'
        }));
    }
}

testHandler();
        `;
    }

    /**
     * Generate script for event monitoring
     */
    generateEventMonitorScript(eventNames, duration) {
        return `
const { ipcMain } = require('electron');

// Load handlers to enable event emission
try {
    require('./main/ipc/ipcHandlers');
    require('./main/ipc/fileHandlers');
    require('./main/ipc/analysisHandlers');
} catch (error) {
    console.log('RESULT:', JSON.stringify({ error: 'Failed to load handlers: ' + error.message }));
    process.exit(1);
}

const events = [];
const eventNames = ${JSON.stringify(eventNames)};

// Mock event object that captures events
const mockEvent = {
    sender: {
        send: (channel, data) => {
            if (eventNames.includes(channel)) {
                events.push({
                    channel: channel,
                    data: data,
                    timestamp: Date.now()
                });
            }
        }
    }
};

// Monitor for specified duration
setTimeout(() => {
    console.log('RESULT:', JSON.stringify({ 
        events: events,
        duration: ${duration}
    }));
    process.exit(0);
}, ${duration});

console.log('Monitoring events for ${duration}ms...');
        `;
    }

    /**
     * Generate script for complete flow testing
     */
    generateFlowTestScript(handlerName, args, expectedEvents, timeout) {
        return `
const { ipcMain } = require('electron');

// Load handlers
try {
    require('./main/ipc/ipcHandlers');
    require('./main/ipc/fileHandlers');
    require('./main/ipc/analysisHandlers');
} catch (error) {
    console.log('RESULT:', JSON.stringify({ error: 'Failed to load handlers: ' + error.message }));
    process.exit(1);
}

const events = [];
const expectedEvents = ${JSON.stringify(expectedEvents)};

// Mock event object
const mockEvent = {
    sender: {
        send: (channel, data) => {
            events.push({
                channel: channel,
                data: data,
                timestamp: Date.now()
            });
            
            // Check if we have all expected events
            const receivedChannels = events.map(e => e.channel);
            const hasAllEvents = expectedEvents.every(expected => 
                receivedChannels.includes(expected)
            );
            
            if (hasAllEvents) {
                console.log('RESULT:', JSON.stringify({
                    success: true,
                    events: events,
                    handlerName: '${handlerName}'
                }));
                process.exit(0);
            }
        }
    }
};

async function testFlow() {
    try {
        // Get and call handler
        const handlers = ipcMain._events || {};
        const handler = handlers['${handlerName}'];
        
        if (!handler) {
            throw new Error('Handler not found');
        }
        
        const result = await handler[0](mockEvent, ${JSON.stringify(args).slice(1, -1)});
        
        // Wait for events or timeout
        setTimeout(() => {
            console.log('RESULT:', JSON.stringify({
                error: 'Timeout waiting for events',
                receivedEvents: events,
                expectedEvents: expectedEvents
            }));
            process.exit(1);
        }, ${timeout});
        
    } catch (error) {
        console.log('RESULT:', JSON.stringify({
            error: error.message,
            handlerName: '${handlerName}'
        }));
        process.exit(1);
    }
}

testFlow();
        `;
    }

    /**
     * Generate script for handler validation
     */
    generateHandlerValidationScript(requiredHandlers) {
        return `
const { ipcMain } = require('electron');

// Load handlers
try {
    require('./main/ipc/ipcHandlers');
    require('./main/ipc/fileHandlers');
    require('./main/ipc/analysisHandlers');
} catch (error) {
    console.log('RESULT:', JSON.stringify({ error: 'Failed to load handlers: ' + error.message }));
    process.exit(1);
}

const requiredHandlers = ${JSON.stringify(requiredHandlers)};
const handlers = ipcMain._events || {};
const available = [];
const missing = [];

requiredHandlers.forEach(handlerName => {
    if (handlers[handlerName]) {
        available.push(handlerName);
    } else {
        missing.push(handlerName);
    }
});

console.log('RESULT:', JSON.stringify({
    available: available,
    missing: missing,
    total: Object.keys(handlers).length
}));
        `;
    }

    /**
     * Generate script for analysis workflow testing
     */
    generateAnalysisWorkflowScript(filePath) {
        return `
const { ipcMain } = require('electron');
const path = require('path');

// Load handlers
try {
    require('./main/ipc/ipcHandlers');
    require('./main/ipc/fileHandlers');
    require('./main/ipc/analysisHandlers');
} catch (error) {
    console.log('RESULT:', JSON.stringify({ error: 'Failed to load handlers: ' + error.message }));
    process.exit(1);
}

const events = [];
const filePath = '${filePath}';

// Mock event object
const mockEvent = {
    sender: {
        send: (channel, data) => {
            events.push({
                channel: channel,
                data: data,
                timestamp: Date.now()
            });
            
            // Check for completion
            if (channel === 'analysis-complete') {
                console.log('RESULT:', JSON.stringify({
                    success: true,
                    events: events,
                    analysisResult: data,
                    filePath: filePath
                }));
                process.exit(0);
            }
        }
    }
};

async function testAnalysis() {
    try {
        // Get analyze-file handler
        const handlers = ipcMain._events || {};
        const handler = handlers['analyze-file'];
        
        if (!handler) {
            throw new Error('analyze-file handler not found');
        }
        
        // Start analysis
        const result = await handler[0](mockEvent, filePath, { calculateWaveform: true });
        
        // Set timeout
        setTimeout(() => {
            console.log('RESULT:', JSON.stringify({
                error: 'Analysis timeout',
                events: events,
                filePath: filePath
            }));
            process.exit(1);
        }, 30000);
        
    } catch (error) {
        console.log('RESULT:', JSON.stringify({
            error: error.message,
            filePath: filePath
        }));
        process.exit(1);
    }
}

testAnalysis();
        `;
    }

    /**
     * Execute test script and parse results
     */
    async executeTestScript(script) {
        const scriptPath = path.join(this.appPath, `temp-ipc-test-${Date.now()}.js`);
        
        try {
            // Write script
            await fs.writeFile(scriptPath, script);
            
            // Execute script
            const result = await this.runScript(scriptPath);
            
            // Cleanup
            await fs.unlink(scriptPath);
            
            return result;
            
        } catch (error) {
            // Cleanup on error
            try {
                await fs.unlink(scriptPath);
            } catch (e) {}
            
            throw error;
        }
    }

    /**
     * Run Node.js script and capture output
     */
    async runScript(scriptPath) {
        return new Promise((resolve, reject) => {
            const process = spawn('node', [scriptPath], {
                cwd: this.appPath,
                stdio: 'pipe'
            });
            
            let output = '';
            let error = '';
            
            process.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            process.stderr.on('data', (data) => {
                error += data.toString();
            });
            
            process.on('close', (code) => {
                try {
                    // Parse results from output
                    const lines = output.split('\n');
                    let result = { events: [] };
                    
                    lines.forEach(line => {
                        if (line.startsWith('RESULT:')) {
                            try {
                                result = { ...result, ...JSON.parse(line.substring(7)) };
                            } catch (e) {}
                        } else if (line.startsWith('EVENT:')) {
                            try {
                                result.events.push(JSON.parse(line.substring(6)));
                            } catch (e) {}
                        }
                    });
                    
                    if (code !== 0 && !result.error) {
                        result.error = `Process exited with code ${code}: ${error}`;
                    }
                    
                    resolve(result);
                    
                } catch (parseError) {
                    reject(new Error(`Failed to parse results: ${parseError.message}`));
                }
            });
        });
    }
}

module.exports = { IPCTester };
