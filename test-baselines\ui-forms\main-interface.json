{"id": "main-interface", "timestamp": "2025-08-01T08:51:49.538Z", "sessionId": "96bcc034-e240-4e61-9d88-8893cf8658d1", "structure": {"formType": "audio-engineering-form", "sections": [{"name": "audio-controls", "elements": [{"id": "waveform-display", "type": "canvas", "className": "waveform"}], "count": 1}, {"name": "meters", "elements": [{"id": "lufs-meter", "type": "div", "className": "lufs-meter"}, {"id": "true-peak-meter", "type": "div", "className": "peak-meter"}], "count": 2}, {"name": "effects", "elements": [{"id": "vst-plugin-slot", "type": "div", "className": "vst-plugin"}], "count": 1}, {"name": "file-handling", "elements": [{"id": "file-drop-zone", "type": "div", "className": "drop-zone"}], "count": 1}], "inputFields": [{"id": "gain-knob", "type": "input", "className": "knob", "required": false, "validation": {"required": false, "pattern": null, "minLength": null, "maxLength": null, "min": null, "max": null, "type": "input"}, "audioSpecific": true}], "audioControls": [{"id": "waveform-display", "type": "unknown", "className": "waveform", "functionality": "playback"}, {"id": "gain-knob", "type": "knob", "className": "knob", "functionality": "gain"}], "vstPluginSlots": [{"id": "vst-plugin-slot", "className": "vst-plugin", "slotNumber": null, "isEmpty": false, "vstType": "unknown"}], "waveformDisplays": [{"id": "waveform-display", "type": "canvas", "className": "waveform", "displayType": "waveform", "interactive": false}], "meterDisplays": [{"id": "lufs-meter", "className": "lufs-meter", "meterType": "lufs", "range": {"min": -70, "max": 0}, "units": "LUFS"}, {"id": "true-peak-meter", "className": "peak-meter", "meterType": "peak", "range": {"min": -60, "max": 0}, "units": "dBTP"}], "navigationElements": []}, "elements": {"buttons": [], "inputs": [], "sliders": [], "knobs": [{"id": "gain-knob", "type": "input", "audioFunction": "unknown"}], "meters": [{"id": "lufs-meter", "type": "div", "audioFunction": "unknown"}, {"id": "true-peak-meter", "type": "div", "audioFunction": "unknown"}], "waveforms": [{"id": "waveform-display", "type": "canvas", "audioFunction": "unknown"}], "vstPlugins": [{"id": "vst-plugin-slot", "type": "div", "audioFunction": "unknown"}], "fileDropZones": [{"id": "file-drop-zone", "type": "div", "audioFunction": "unknown"}], "audioPlayers": []}, "layout": {"gridSystem": "css-grid", "responsiveBreakpoints": [], "audioWorkflowLayout": {}, "vstPluginLayout": {}, "meterPlacement": {}, "waveformLayout": {}, "controlGrouping": {}}, "audioSpecific": {"supportedFormats": ["wav", "flac", "mp3", "aac", "ogg"], "sampleRateSupport": [44100, 48000, 96000, 192000], "bitDepthSupport": [16, 24, 32], "lufsMetering": false, "truePeakMetering": false, "spectralAnalysis": false, "realtimeProcessing": false}, "vstCompatibility": {"vstSlots": [{"id": "vst-plugin-slot", "className": "vst-plugin", "slotNumber": null, "isEmpty": false, "vstType": "unknown"}], "supportedFormats": ["VST2", "VST3", "AU", "AAX"], "parameterMapping": {}, "presetManagement": {}, "bypassControls": {}, "latencyCompensation": {}}, "accessibility": {"keyboardNavigation": true, "screenReaderSupport": true, "colorContrast": true, "audioFeedback": false, "visualIndicators": true, "alternativeInputs": false}, "performance": {"renderTime": 0, "memoryUsage": 0, "cpuUsage": 0, "audioLatency": 0, "vstLoadTime": 0, "waveformRenderTime": 0}, "hash": "90f4c47f54d8f9c478c4c22c2565214cae94810619c183d0cf0f927cc54e9115"}