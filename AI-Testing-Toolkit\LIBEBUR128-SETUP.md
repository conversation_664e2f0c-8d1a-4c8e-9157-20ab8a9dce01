# 🎵 Professional LUFS Setup with libebur128

## Overview

The testing framework now uses **libebur128** for professional LUFS calculation and **4x oversampling for True Peak** measurement, meeting broadcast and streaming industry standards.

## 🔧 Installation Guide

### **Windows Setup**

#### **Option 1: FFmpeg with libebur128 (Recommended)**
```bash
# Download FFmpeg with libebur128 support
# Visit: https://www.gyan.dev/ffmpeg/builds/
# Download the "full" build which includes libebur128

# Or use Chocolatey
choco install ffmpeg-full

# Verify installation
ffmpeg -filters | grep loudnorm
```

### **macOS Setup**

```bash
# Using Homebrew
brew install ffmpeg --with-libebur128

# Verify installation
ffmpeg -filters | grep loudnorm
```

### **Linux Setup**

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg libebur128-dev

# Verify installation
ffmpeg -filters | grep loudnorm
```

## 🎯 **What This Provides**

### **Professional LUFS Calculation**
- **EBU R128 compliant** loudness measurement
- **ITU-R BS.1770-4** algorithm implementation
- **Integrated, Momentary, and Short-term** LUFS
- **Loudness Range (LRA)** measurement

### **True Peak with 4x Oversampling**
- **4x oversampling** for accurate True Peak detection
- **Inter-sample peak detection** to prevent digital clipping
- **Broadcast-safe** True Peak limiting
- **Streaming platform compliance**

### **Industry Standards Supported**
- **Spotify**: -14 LUFS, -1 dBTP
- **Apple Music**: -16 LUFS, -1 dBTP
- **YouTube**: -14 LUFS, -1 dBTP
- **Broadcast**: -23 LUFS, -1 dBTP
- **Mastering**: Custom targets with professional accuracy

## 🧪 **Testing Framework Integration**

### **Automatic Detection**
```javascript
const validator = new LUFSValidator();
await validator.initialize(); // Automatically detects libebur128

if (validator.libebur128Available) {
    console.log('✅ Professional LUFS calculation available');
} else {
    console.log('⚠️ Using estimation - install libebur128 for accuracy');
}
```

### **Professional LUFS Analysis**
```javascript
const result = await validator.calculateReferenceLUFS('audio.wav');

console.log(`Integrated LUFS: ${result.integrated}`);
console.log(`True Peak: ${result.truePeak} dBTP`);
console.log(`4x Oversampled: ${result.truePeakOversampled}`);
console.log(`Method: ${result.method}`); // 'libebur128-professional'
```

## 📊 **Output Format**

### **Professional LUFS Result**
```json
{
    "integrated": -14.2,
    "momentary": -13.8,
    "shortTerm": -14.0,
    "range": 6.4,
    "truePeak": -0.8,
    "truePeakOversampled": true,
    "method": "libebur128-professional",
    "oversamplingFactor": 4,
    "note": "Professional calculation using libebur128 with 4x oversampling for True Peak"
}
```

### **Fallback Estimation (when libebur128 unavailable)**
```json
{
    "integrated": -15.1,
    "momentary": -14.9,
    "shortTerm": -15.0,
    "range": 7.2,
    "truePeak": -1.2,
    "truePeakOversampled": false,
    "method": "estimation-fallback",
    "oversamplingFactor": 1,
    "note": "Estimation used - install ffmpeg with libebur128 for professional calculation"
}
```

## ✅ **Verification**

### **Test Professional LUFS**
```bash
# Run the framework test
node AI-Testing-Toolkit/demo-comprehensive-testing.js

# Look for:
# ✅ libebur128 (via ffmpeg) available for professional LUFS calculation
# 📊 Calculating professional LUFS for: test.wav
# Method: libebur128-professional
# 4x Oversampled: true
```

**🎉 Result**: Professional-grade LUFS calculation with 4x oversampling for True Peak!