/**
 * 🤖 AI Agent Electron Application Tester
 * 
 * Comprehensive testing tool for Electron applications.
 * Provides programmatic control over Electron app lifecycle,
 * IPC communication testing, and process monitoring.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;

class ElectronTester {
    constructor(appPath, options = {}) {
        this.appPath = appPath;
        this.options = {
            timeout: 30000,
            debug: true,
            autoRestart: true,
            ...options
        };
        
        this.process = null;
        this.isRunning = false;
        this.logs = [];
        this.events = [];
    }

    /**
     * Start the Electron application
     */
    async start() {
        if (this.isRunning) {
            await this.stop();
        }

        console.log('🚀 Starting Electron application...');
        
        try {
            // Start the application process
            this.process = spawn('npm', ['start'], {
                cwd: this.appPath,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            // Capture output
            this.process.stdout.on('data', (data) => {
                const log = data.toString();
                this.logs.push({ type: 'stdout', data: log, timestamp: Date.now() });
                if (this.options.debug) console.log('📤 STDOUT:', log.trim());
            });

            this.process.stderr.on('data', (data) => {
                const log = data.toString();
                this.logs.push({ type: 'stderr', data: log, timestamp: Date.now() });
                if (this.options.debug) console.log('📤 STDERR:', log.trim());
            });

            this.process.on('close', (code) => {
                console.log(`🔚 Electron process exited with code ${code}`);
                this.isRunning = false;
            });

            // Wait for application to be ready
            await this.waitForReady();
            this.isRunning = true;
            
            console.log('✅ Electron application started successfully');
            return true;

        } catch (error) {
            console.error('❌ Failed to start Electron application:', error);
            throw error;
        }
    }

    /**
     * Stop the Electron application
     */
    async stop() {
        if (this.process && this.isRunning) {
            console.log('🛑 Stopping Electron application...');
            this.process.kill();
            this.isRunning = false;
            
            // Wait for process to exit
            await new Promise(resolve => {
                if (this.process.killed) {
                    resolve();
                } else {
                    this.process.on('close', resolve);
                }
            });
            
            console.log('✅ Electron application stopped');
        }
    }

    /**
     * Wait for application to be ready
     */
    async waitForReady(timeout = this.options.timeout) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            // Check for ready indicators in logs
            const readyIndicators = [
                'webpack compiled successfully',
                'Analysis handlers loaded',
                'IPC handlers loaded'
            ];
            
            const hasAllIndicators = readyIndicators.every(indicator =>
                this.logs.some(log => log.data.includes(indicator))
            );
            
            if (hasAllIndicators) {
                return true;
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error('Application failed to start within timeout');
    }

    /**
     * Test IPC handler directly
     */
    async testIPCHandler(handlerName, ...args) {
        console.log(`🔧 Testing IPC handler: ${handlerName}`);
        
        try {
            // Create a test script that calls the handler
            const testScript = `
                const { ipcMain } = require('electron');
                
                // Load handlers
                require('./main/ipc/ipcHandlers');
                require('./main/ipc/fileHandlers');
                require('./main/ipc/analysisHandlers');
                
                // Mock event
                const mockEvent = {
                    sender: {
                        send: (channel, data) => {
                            console.log('IPC_EVENT:', JSON.stringify({ channel, data }));
                        }
                    }
                };
                
                // Get handler
                const handlers = ipcMain._events || {};
                const handler = handlers['${handlerName}'];
                
                if (!handler) {
                    console.log('IPC_ERROR: Handler not found');
                    process.exit(1);
                }
                
                // Call handler
                handler[0](mockEvent, ${JSON.stringify(args).slice(1, -1)})
                    .then(result => {
                        console.log('IPC_RESULT:', JSON.stringify(result));
                        process.exit(0);
                    })
                    .catch(error => {
                        console.log('IPC_ERROR:', error.message);
                        process.exit(1);
                    });
            `;
            
            // Write and execute test script
            const scriptPath = path.join(this.appPath, 'temp-ipc-test.js');
            await fs.writeFile(scriptPath, testScript);
            
            const result = await this.executeScript(scriptPath);
            
            // Cleanup
            await fs.unlink(scriptPath);
            
            return result;
            
        } catch (error) {
            console.error(`❌ IPC handler test failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Execute a Node.js script in the app context
     */
    async executeScript(scriptPath) {
        return new Promise((resolve, reject) => {
            const process = spawn('node', [scriptPath], {
                cwd: this.appPath,
                stdio: 'pipe'
            });
            
            let output = '';
            let error = '';
            
            process.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            process.stderr.on('data', (data) => {
                error += data.toString();
            });
            
            process.on('close', (code) => {
                if (code === 0) {
                    // Parse IPC results from output
                    const lines = output.split('\n');
                    const results = {
                        events: [],
                        result: null,
                        error: null
                    };
                    
                    lines.forEach(line => {
                        if (line.startsWith('IPC_EVENT:')) {
                            try {
                                results.events.push(JSON.parse(line.substring(10)));
                            } catch (e) {}
                        } else if (line.startsWith('IPC_RESULT:')) {
                            try {
                                results.result = JSON.parse(line.substring(11));
                            } catch (e) {}
                        } else if (line.startsWith('IPC_ERROR:')) {
                            results.error = line.substring(10);
                        }
                    });
                    
                    resolve(results);
                } else {
                    reject(new Error(`Script failed with code ${code}: ${error}`));
                }
            });
        });
    }

    /**
     * Get application logs
     */
    getLogs(filter = null) {
        if (filter) {
            return this.logs.filter(log => 
                log.data.toLowerCase().includes(filter.toLowerCase())
            );
        }
        return this.logs;
    }

    /**
     * Wait for specific log message
     */
    async waitForLog(message, timeout = 10000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const found = this.logs.some(log => 
                log.data.includes(message)
            );
            
            if (found) {
                return true;
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error(`Log message "${message}" not found within timeout`);
    }

    /**
     * Check if application is healthy
     */
    isHealthy() {
        return this.isRunning && this.process && !this.process.killed;
    }

    /**
     * Get performance metrics
     */
    getMetrics() {
        return {
            isRunning: this.isRunning,
            uptime: this.isRunning ? Date.now() - this.startTime : 0,
            logCount: this.logs.length,
            errorCount: this.logs.filter(log => log.type === 'stderr').length,
            memoryUsage: process.memoryUsage()
        };
    }
}

module.exports = { ElectronTester };
