/**
 * 🤖 AI Agent Audio File Generator
 * 
 * Generates test audio files with specific properties for testing.
 * Creates files with known LUFS values, True Peak levels,
 * and various formats for comprehensive testing.
 */

const fs = require('fs').promises;
const path = require('path');

class AudioFileGenerator {
    constructor(options = {}) {
        this.options = {
            outputDir: './test-audio-files',
            sampleRate: 48000,
            bitDepth: 24,
            ...options
        };
        
        this.generatedFiles = [];
    }

    /**
     * Create test audio file with specific properties
     */
    async createTestFile(config) {
        const {
            name = 'test-audio',
            duration = 10,
            sampleRate = this.options.sampleRate,
            channels = 2,
            lufs = -14.0,
            truePeak = -1.0,
            format = 'wav',
            frequency = 1000
        } = config;
        
        console.log(`🎵 Generating test audio file: ${name}.${format}`);
        console.log(`   Duration: ${duration}s, LUFS: ${lufs}, True Peak: ${truePeak}dBTP`);
        
        // Ensure output directory exists
        await this.ensureOutputDir();
        
        // Generate audio data
        const audioData = this.generateAudioData({
            duration,
            sampleRate,
            channels,
            lufs,
            truePeak,
            frequency
        });
        
        // Create file path
        const fileName = `${name}.${format}`;
        const filePath = path.join(this.options.outputDir, fileName);
        
        // Write audio file
        await this.writeAudioFile(filePath, audioData, format, {
            sampleRate,
            channels,
            bitDepth: this.options.bitDepth
        });
        
        // Store file info
        const fileInfo = {
            path: filePath,
            name: fileName,
            duration,
            sampleRate,
            channels,
            lufs,
            truePeak,
            format,
            size: (await fs.stat(filePath)).size,
            created: Date.now()
        };
        
        this.generatedFiles.push(fileInfo);
        
        console.log(`✅ Generated: ${fileName} (${fileInfo.size} bytes)`);
        return fileInfo;
    }

    /**
     * Create a set of test files for comprehensive testing
     */
    async createTestSuite() {
        console.log('🎼 Creating comprehensive test suite...');
        
        const testConfigs = [
            // Standard Spotify target
            { name: 'spotify-target', lufs: -14.0, truePeak: -1.0, duration: 5 },
            
            // Quiet file needing gain
            { name: 'quiet-file', lufs: -23.0, truePeak: -6.0, duration: 5 },
            
            // Loud file needing attenuation
            { name: 'loud-file', lufs: -8.0, truePeak: -0.1, duration: 5 },
            
            // True peak limited
            { name: 'peak-limited', lufs: -12.0, truePeak: -0.1, duration: 5 },
            
            // LUFS limited
            { name: 'lufs-limited', lufs: -14.0, truePeak: -3.0, duration: 5 },
            
            // Different formats
            { name: 'test-mp3', lufs: -16.0, format: 'mp3', duration: 3 },
            { name: 'test-flac', lufs: -18.0, format: 'flac', duration: 3 },
            
            // Different durations
            { name: 'short-file', lufs: -15.0, duration: 1 },
            { name: 'long-file', lufs: -13.0, duration: 30 },
            
            // Different frequencies
            { name: 'low-freq', lufs: -14.0, frequency: 100, duration: 5 },
            { name: 'high-freq', lufs: -14.0, frequency: 8000, duration: 5 },
            
            // Mono file
            { name: 'mono-file', lufs: -14.0, channels: 1, duration: 5 }
        ];
        
        const results = [];
        for (const config of testConfigs) {
            try {
                const result = await this.createTestFile(config);
                results.push(result);
            } catch (error) {
                console.error(`❌ Failed to create ${config.name}:`, error.message);
            }
        }
        
        console.log(`✅ Created ${results.length} test files`);
        return results;
    }

    /**
     * Generate audio data with specific LUFS and True Peak
     */
    generateAudioData(config) {
        const { duration, sampleRate, channels, lufs, truePeak, frequency } = config;
        
        const numSamples = Math.floor(duration * sampleRate);
        const audioData = new Float32Array(numSamples * channels);
        
        // Calculate amplitude for target LUFS
        // LUFS is roughly -23 + 20*log10(RMS)
        const targetRMS = Math.pow(10, (lufs + 23) / 20);
        
        // Calculate amplitude for target True Peak
        const targetPeakAmplitude = Math.pow(10, truePeak / 20);
        
        // Use the more restrictive limit
        const amplitude = Math.min(targetRMS * Math.sqrt(2), targetPeakAmplitude);
        
        // Generate sine wave
        for (let i = 0; i < numSamples; i++) {
            const t = i / sampleRate;
            const sample = amplitude * Math.sin(2 * Math.PI * frequency * t);
            
            // Apply slight randomization to avoid pure tone
            const noise = (Math.random() - 0.5) * 0.001;
            const finalSample = sample + noise;
            
            // Write to all channels
            for (let ch = 0; ch < channels; ch++) {
                audioData[i * channels + ch] = finalSample;
            }
        }
        
        return audioData;
    }

    /**
     * Write audio data to file
     */
    async writeAudioFile(filePath, audioData, format, metadata) {
        switch (format.toLowerCase()) {
            case 'wav':
                await this.writeWavFile(filePath, audioData, metadata);
                break;
            case 'mp3':
                await this.writeMp3File(filePath, audioData, metadata);
                break;
            case 'flac':
                await this.writeFlacFile(filePath, audioData, metadata);
                break;
            default:
                throw new Error(`Unsupported format: ${format}`);
        }
    }

    /**
     * Write WAV file
     */
    async writeWavFile(filePath, audioData, metadata) {
        const { sampleRate, channels, bitDepth } = metadata;
        
        // Convert float32 to int16/int24
        const bytesPerSample = bitDepth / 8;
        const maxValue = Math.pow(2, bitDepth - 1) - 1;
        
        const pcmData = new ArrayBuffer(audioData.length * bytesPerSample);
        const view = new DataView(pcmData);
        
        for (let i = 0; i < audioData.length; i++) {
            const sample = Math.max(-1, Math.min(1, audioData[i]));
            const intSample = Math.round(sample * maxValue);
            
            if (bitDepth === 16) {
                view.setInt16(i * 2, intSample, true);
            } else if (bitDepth === 24) {
                view.setInt32(i * 3, intSample << 8, true);
            }
        }
        
        // Create WAV header
        const header = this.createWavHeader(pcmData.byteLength, sampleRate, channels, bitDepth);
        
        // Combine header and data
        const wavFile = new Uint8Array(header.byteLength + pcmData.byteLength);
        wavFile.set(new Uint8Array(header), 0);
        wavFile.set(new Uint8Array(pcmData), header.byteLength);
        
        await fs.writeFile(filePath, wavFile);
    }

    /**
     * Create WAV file header
     */
    createWavHeader(dataSize, sampleRate, channels, bitDepth) {
        const header = new ArrayBuffer(44);
        const view = new DataView(header);
        
        // RIFF header
        view.setUint32(0, 0x46464952, false); // "RIFF"
        view.setUint32(4, dataSize + 36, true); // File size - 8
        view.setUint32(8, 0x45564157, false); // "WAVE"
        
        // Format chunk
        view.setUint32(12, 0x20746d66, false); // "fmt "
        view.setUint32(16, 16, true); // Chunk size
        view.setUint16(20, 1, true); // Audio format (PCM)
        view.setUint16(22, channels, true); // Number of channels
        view.setUint32(24, sampleRate, true); // Sample rate
        view.setUint32(28, sampleRate * channels * (bitDepth / 8), true); // Byte rate
        view.setUint16(32, channels * (bitDepth / 8), true); // Block align
        view.setUint16(34, bitDepth, true); // Bits per sample
        
        // Data chunk
        view.setUint32(36, 0x61746164, false); // "data"
        view.setUint32(40, dataSize, true); // Data size
        
        return header;
    }

    /**
     * Write MP3 file (simplified - would need FFmpeg in real implementation)
     */
    async writeMp3File(filePath, audioData, metadata) {
        // For now, create a WAV file and note that it should be converted
        const wavPath = filePath.replace('.mp3', '.wav');
        await this.writeWavFile(wavPath, audioData, metadata);
        
        console.log(`⚠️ Created WAV file instead of MP3. Use FFmpeg to convert: ${wavPath}`);
        
        // In a real implementation, you would use FFmpeg here:
        // ffmpeg -i input.wav -codec:a libmp3lame -b:a 320k output.mp3
    }

    /**
     * Write FLAC file (simplified - would need FFmpeg in real implementation)
     */
    async writeFlacFile(filePath, audioData, metadata) {
        // For now, create a WAV file and note that it should be converted
        const wavPath = filePath.replace('.flac', '.wav');
        await this.writeWavFile(wavPath, audioData, metadata);
        
        console.log(`⚠️ Created WAV file instead of FLAC. Use FFmpeg to convert: ${wavPath}`);
        
        // In a real implementation, you would use FFmpeg here:
        // ffmpeg -i input.wav -codec:a flac output.flac
    }

    /**
     * Ensure output directory exists
     */
    async ensureOutputDir() {
        try {
            await fs.access(this.options.outputDir);
        } catch (error) {
            await fs.mkdir(this.options.outputDir, { recursive: true });
            console.log(`📁 Created output directory: ${this.options.outputDir}`);
        }
    }

    /**
     * Get list of generated files
     */
    getGeneratedFiles() {
        return this.generatedFiles;
    }

    /**
     * Clean up generated files
     */
    async cleanup() {
        console.log('🧹 Cleaning up generated test files...');
        
        for (const file of this.generatedFiles) {
            try {
                await fs.unlink(file.path);
                console.log(`🗑️ Deleted: ${file.name}`);
            } catch (error) {
                console.warn(`⚠️ Failed to delete ${file.name}:`, error.message);
            }
        }
        
        this.generatedFiles = [];
        console.log('✅ Cleanup completed');
    }

    /**
     * Validate generated file properties
     */
    async validateFile(filePath, expectedProperties) {
        console.log(`🔍 Validating file: ${path.basename(filePath)}`);
        
        // This would use a real audio analysis library in production
        // For now, return mock validation
        return {
            valid: true,
            properties: expectedProperties,
            message: 'File validation would require audio analysis library'
        };
    }
}

module.exports = { AudioFileGenerator };
