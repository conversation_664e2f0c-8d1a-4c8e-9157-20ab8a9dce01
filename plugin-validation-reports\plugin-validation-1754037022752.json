{"timestamp": "2025-08-01T08:30:22.752Z", "session": {"id": "dc8adf22-46ab-4700-9035-50062269511d", "timestamp": "2025-08-01T08:30:11.721Z", "testsRun": 0, "pluginsTested": 5}, "summary": {"totalPlugins": 5, "testedPlugins": 5, "passedPlugins": 3, "failedPlugins": 2}, "results": [{"pluginId": "d1b43839-fa9e-4439-80fa-2e14382f6f25", "pluginName": "EQ Eight", "pluginPath": "/plugins/eq eight.vst3", "format": "VST3", "timestamp": "2025-08-01T08:30:11.797Z", "tests": {"loadTest": {"passed": true, "loadTime": 668, "threshold": 3000, "message": "Plugin loaded successfully"}, "parameterTest": {"passed": true, "parameterCount": 5, "tests": [{"parameterId": 0, "parameterName": "<PERSON><PERSON>", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 15.33804563851855}, {"parameterId": 1, "parameterName": "Frequency", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 62.96666910997697}, {"parameterId": 2, "parameterName": "Resonance", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 45.362498671091636}, {"parameterId": 3, "parameterName": "Attack", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 71.95838067229217}, {"parameterId": 4, "parameterName": "Release", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 36.0784332795864}], "message": "Tested 5 parameters"}, "audioProcessingTest": {"passed": true, "tests": [{"testFile": "./test-audio/sine-440hz-44k.wav", "passed": true, "processingTime": 57.92351142681713, "outputQuality": 0.9951811671156211, "latencyAdded": 5.286500758600114}, {"testFile": "./test-audio/white-noise-48k.wav", "passed": true, "processingTime": 59.2339083540546, "outputQuality": 0.9368253071779415, "latencyAdded": 4.735393508147599}, {"testFile": "./test-audio/music-sample-96k.wav", "passed": true, "processingTime": 146.6911773146988, "outputQuality": 0.9760636759602197, "latencyAdded": 5.600833149124481}], "message": "Processed 3 test files"}, "stabilityTest": {"passed": true, "crashCount": 0, "memoryLeaks": 0, "stressTestDuration": 500, "message": "Stability test completed"}}, "performance": {"cpuUsage": 10.66467720747787, "memoryUsage": 36.53784924854425, "latency": 8.858568671260464, "throughput": 13.326556855410498, "passed": true}, "compatibility": {"hostCompatibility": true, "formatSupport": "VST3", "versionCompatibility": true, "osCompatibility": true, "passed": true}, "passed": true, "issues": [], "recommendations": []}, {"pluginId": "91cc2727-a0c1-4464-abec-bec9601d2f86", "pluginName": "Compressor", "pluginPath": "/plugins/compressor.vst3", "format": "VST3", "timestamp": "2025-08-01T08:30:13.860Z", "tests": {"loadTest": {"passed": true, "loadTime": 696, "threshold": 3000, "message": "Plugin loaded successfully"}, "parameterTest": {"passed": false, "parameterCount": 9, "tests": [{"parameterId": 0, "parameterName": "<PERSON><PERSON>", "passed": false, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 68.28763926172078}, {"parameterId": 1, "parameterName": "Frequency", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 85.0988951064972}, {"parameterId": 2, "parameterName": "Resonance", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 22.52072147282003}, {"parameterId": 3, "parameterName": "Attack", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 10.092655265431262}, {"parameterId": 4, "parameterName": "Release", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 22.69385005948168}, {"parameterId": 5, "parameterName": "<PERSON><PERSON><PERSON><PERSON>", "passed": false, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 85.61487145241402}, {"parameterId": 6, "parameterName": "<PERSON><PERSON>", "passed": false, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 46.02641672868888}, {"parameterId": 7, "parameterName": "Mix", "passed": false, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 4.990773953966543}, {"parameterId": 8, "parameterName": "Gain 2", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 53.5047346981556}], "message": "Tested 9 parameters"}, "audioProcessingTest": {"passed": true, "tests": [{"testFile": "./test-audio/sine-440hz-44k.wav", "passed": true, "processingTime": 96.73328576355613, "outputQuality": 0.9920109424054112, "latencyAdded": 3.871990309138348}, {"testFile": "./test-audio/white-noise-48k.wav", "passed": true, "processingTime": 75.40522644041928, "outputQuality": 0.918989440277793, "latencyAdded": 3.431288851576454}, {"testFile": "./test-audio/music-sample-96k.wav", "passed": true, "processingTime": 53.260583440982145, "outputQuality": 0.9191692460509622, "latencyAdded": 5.620193302782559}], "message": "Processed 3 test files"}, "stabilityTest": {"passed": true, "crashCount": 0, "memoryLeaks": 0, "stressTestDuration": 500, "message": "Stability test completed"}}, "performance": {"cpuUsage": 5.226778170949563, "memoryUsage": 29.096637258544444, "latency": 2.246258798143911, "throughput": 10.936458848687016, "passed": true}, "compatibility": {"hostCompatibility": true, "formatSupport": "VST3", "versionCompatibility": true, "osCompatibility": true, "passed": true}, "passed": false, "issues": [{"type": "parameterTest", "severity": "medium", "description": "Tested 9 parameters"}], "recommendations": [{"priority": "high", "message": "Plugin validation failed - review compatibility and performance"}, {"priority": "medium", "message": "Address identified issues to improve plugin reliability"}]}, {"pluginId": "4835a8c6-07ce-4e03-8344-482bbcf3d823", "pluginName": "Reverb", "pluginPath": "/plugins/reverb.vst2", "format": "VST2", "timestamp": "2025-08-01T08:30:16.122Z", "tests": {"loadTest": {"passed": true, "loadTime": 691, "threshold": 3000, "message": "Plugin loaded successfully"}, "parameterTest": {"passed": false, "parameterCount": 11, "tests": [{"parameterId": 0, "parameterName": "<PERSON><PERSON>", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 49.72587641619084}, {"parameterId": 1, "parameterName": "Frequency", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 98.62244707169003}, {"parameterId": 2, "parameterName": "Resonance", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 92.02496118203788}, {"parameterId": 3, "parameterName": "Attack", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 84.24414221215197}, {"parameterId": 4, "parameterName": "Release", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 78.77977473551216}, {"parameterId": 5, "parameterName": "<PERSON><PERSON><PERSON><PERSON>", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 88.87543244733209}, {"parameterId": 6, "parameterName": "<PERSON><PERSON>", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 6.1008460613454}, {"parameterId": 7, "parameterName": "Mix", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 24.789294013646334}, {"parameterId": 8, "parameterName": "Gain 2", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 19.409687427528265}, {"parameterId": 9, "parameterName": "Frequency 2", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 60.63756933481814}, {"parameterId": 10, "parameterName": "Resonance 2", "passed": false, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 63.73529731628225}], "message": "Tested 11 parameters"}, "audioProcessingTest": {"passed": true, "tests": [{"testFile": "./test-audio/sine-440hz-44k.wav", "passed": true, "processingTime": 104.15365575710145, "outputQuality": 0.9826588104768779, "latencyAdded": 6.543338235591575}, {"testFile": "./test-audio/white-noise-48k.wav", "passed": true, "processingTime": 146.51705990994168, "outputQuality": 0.9185485367192411, "latencyAdded": 5.494771370900103}, {"testFile": "./test-audio/music-sample-96k.wav", "passed": true, "processingTime": 52.15278487710282, "outputQuality": 0.971124466313006, "latencyAdded": 5.369831515298635}], "message": "Processed 3 test files"}, "stabilityTest": {"passed": true, "crashCount": 0, "memoryLeaks": 0, "stressTestDuration": 500, "message": "Stability test completed"}}, "performance": {"cpuUsage": 10.798293001062437, "memoryUsage": 58.883651416517736, "latency": 3.881045295845537, "throughput": 14.681753811879384, "passed": true}, "compatibility": {"hostCompatibility": true, "formatSupport": "VST2", "versionCompatibility": true, "osCompatibility": true, "passed": true}, "passed": false, "issues": [{"type": "parameterTest", "severity": "medium", "description": "Tested 11 parameters"}], "recommendations": [{"priority": "high", "message": "Plugin validation failed - review compatibility and performance"}, {"priority": "medium", "message": "Address identified issues to improve plugin reliability"}]}, {"pluginId": "6564265a-c7a8-4434-91b1-e7b4ac50800a", "pluginName": "Delay", "pluginPath": "/plugins/delay.au", "format": "AU", "timestamp": "2025-08-01T08:30:18.481Z", "tests": {"loadTest": {"passed": true, "loadTime": 592, "threshold": 3000, "message": "Plugin loaded successfully"}, "parameterTest": {"passed": true, "parameterCount": 4, "tests": [{"parameterId": 0, "parameterName": "<PERSON><PERSON>", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 13.51615995051576}, {"parameterId": 1, "parameterName": "Frequency", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 78.02985225414028}, {"parameterId": 2, "parameterName": "Resonance", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 38.23308165948283}, {"parameterId": 3, "parameterName": "Attack", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 26.073257349535695}], "message": "Tested 4 parameters"}, "audioProcessingTest": {"passed": true, "tests": [{"testFile": "./test-audio/sine-440hz-44k.wav", "passed": true, "processingTime": 54.38965031892919, "outputQuality": 0.9384576640094207, "latencyAdded": 4.974381411974431}, {"testFile": "./test-audio/white-noise-48k.wav", "passed": true, "processingTime": 102.56794946952259, "outputQuality": 0.9736817249147226, "latencyAdded": 6.200968102414344}, {"testFile": "./test-audio/music-sample-96k.wav", "passed": true, "processingTime": 148.26245992424685, "outputQuality": 0.937266041085012, "latencyAdded": 3.785252689261317}], "message": "Processed 3 test files"}, "stabilityTest": {"passed": true, "crashCount": 0, "memoryLeaks": 0, "stressTestDuration": 500, "message": "Stability test completed"}}, "performance": {"cpuUsage": 12.506441800718749, "memoryUsage": 39.2377947368126, "latency": 7.296040898198527, "throughput": 13.433529917014638, "passed": true}, "compatibility": {"hostCompatibility": true, "formatSupport": "AU", "versionCompatibility": true, "osCompatibility": true, "passed": true}, "passed": true, "issues": [], "recommendations": []}, {"pluginId": "517bcab2-73f8-46dd-a1f9-f9386451e057", "pluginName": "Distortion", "pluginPath": "/plugins/distortion.vst3", "format": "VST3", "timestamp": "2025-08-01T08:30:20.386Z", "tests": {"loadTest": {"passed": true, "loadTime": 698, "threshold": 3000, "message": "Plugin loaded successfully"}, "parameterTest": {"passed": true, "parameterCount": 11, "tests": [{"parameterId": 0, "parameterName": "<PERSON><PERSON>", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 0.6881756099036851}, {"parameterId": 1, "parameterName": "Frequency", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 46.759760405743414}, {"parameterId": 2, "parameterName": "Resonance", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 51.40354280792967}, {"parameterId": 3, "parameterName": "Attack", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 8.477323807484805}, {"parameterId": 4, "parameterName": "Release", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 38.81480440736078}, {"parameterId": 5, "parameterName": "<PERSON><PERSON><PERSON><PERSON>", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 70.7126324379887}, {"parameterId": 6, "parameterName": "<PERSON><PERSON>", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 56.93519125460358}, {"parameterId": 7, "parameterName": "Mix", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 42.15598444062536}, {"parameterId": 8, "parameterName": "Gain 2", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 64.84892771424798}, {"parameterId": 9, "parameterName": "Frequency 2", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 34.22249970029747}, {"parameterId": 10, "parameterName": "Resonance 2", "passed": true, "testType": "range_validation", "minValue": 0, "maxValue": 100, "testedValue": 85.8732556307167}], "message": "Tested 11 parameters"}, "audioProcessingTest": {"passed": true, "tests": [{"testFile": "./test-audio/sine-440hz-44k.wav", "passed": true, "processingTime": 94.5679134933824, "outputQuality": 0.9601713634725736, "latencyAdded": 2.100390271275097}, {"testFile": "./test-audio/white-noise-48k.wav", "passed": true, "processingTime": 117.80932603561868, "outputQuality": 0.909382063955165, "latencyAdded": 2.68504070515281}, {"testFile": "./test-audio/music-sample-96k.wav", "passed": true, "processingTime": 113.62920461426928, "outputQuality": 0.9682636977003035, "latencyAdded": 6.783676249879633}], "message": "Processed 3 test files"}, "stabilityTest": {"passed": true, "crashCount": 0, "memoryLeaks": 0, "stressTestDuration": 500, "message": "Stability test completed"}}, "performance": {"cpuUsage": 13.788289687256201, "memoryUsage": 42.41688382431856, "latency": 4.4617428857423285, "throughput": 12.548855294975343, "passed": true}, "compatibility": {"hostCompatibility": true, "formatSupport": "VST3", "versionCompatibility": true, "osCompatibility": true, "passed": true}, "passed": true, "issues": [], "recommendations": []}], "recommendations": [{"priority": "high", "message": "2 plugins failed validation - review and update"}, {"priority": "medium", "message": "Regular plugin validation recommended for stability"}]}