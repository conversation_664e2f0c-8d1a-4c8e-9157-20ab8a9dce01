#!/usr/bin/env node

/**
 * 🤖 AI Testing Toolkit - Setup Script
 * 
 * Automatically configures the toolkit for your project
 * and installs necessary dependencies.
 */

const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

class ToolkitSetup {
    constructor(targetPath = process.cwd()) {
        this.targetPath = targetPath;
        this.toolkitPath = __dirname;
        
        console.log('🤖 AI Testing Toolkit Setup');
        console.log(`📁 Target project: ${this.targetPath}`);
        console.log(`🔧 Toolkit location: ${this.toolkitPath}\n`);
    }

    /**
     * Run complete setup process
     */
    async setup() {
        try {
            console.log('🚀 Starting setup process...\n');
            
            // Step 1: Validate target project
            await this.validateProject();
            
            // Step 2: Install dependencies
            await this.installDependencies();
            
            // Step 3: Create configuration
            await this.createConfiguration();
            
            // Step 4: Create test directories
            await this.createDirectories();
            
            // Step 5: Generate example files
            await this.generateExamples();
            
            // Step 6: Validate setup
            await this.validateSetup();
            
            console.log('✅ Setup completed successfully!\n');
            this.printUsageInstructions();
            
        } catch (error) {
            console.error('❌ Setup failed:', error.message);
            process.exit(1);
        }
    }

    /**
     * Validate target project structure
     */
    async validateProject() {
        console.log('🔍 Validating project structure...');
        
        try {
            // Check if package.json exists
            const packageJsonPath = path.join(this.targetPath, 'package.json');
            await fs.access(packageJsonPath);
            
            // Read package.json
            const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
            
            // Check for Electron
            const hasElectron = packageJson.dependencies?.electron || 
                              packageJson.devDependencies?.electron;
            
            if (!hasElectron) {
                console.warn('⚠️ Electron not found in dependencies');
            }
            
            // Check for React
            const hasReact = packageJson.dependencies?.react || 
                           packageJson.devDependencies?.react;
            
            if (!hasReact) {
                console.warn('⚠️ React not found in dependencies');
            }
            
            console.log('✅ Project structure validated');
            
        } catch (error) {
            throw new Error(`Invalid project structure: ${error.message}`);
        }
    }

    /**
     * Install toolkit dependencies
     */
    async installDependencies() {
        console.log('📦 Installing dependencies...');
        
        try {
            // Install Playwright
            await this.runCommand('npm', ['install', 'playwright'], this.toolkitPath);
            
            // Install Playwright browsers
            await this.runCommand('npx', ['playwright', 'install'], this.toolkitPath);
            
            console.log('✅ Dependencies installed');
            
        } catch (error) {
            throw new Error(`Failed to install dependencies: ${error.message}`);
        }
    }

    /**
     * Create project-specific configuration
     */
    async createConfiguration() {
        console.log('⚙️ Creating configuration...');
        
        try {
            // Detect project structure
            const projectType = await this.detectProjectType();
            console.log(`📋 Detected project type: ${projectType}`);
            
            // Create custom config
            const config = await this.generateProjectConfig(projectType);
            
            // Write config file
            const configPath = path.join(this.targetPath, 'ai-testing-config.js');
            await fs.writeFile(configPath, config);
            
            console.log(`✅ Configuration created: ${configPath}`);
            
        } catch (error) {
            throw new Error(`Failed to create configuration: ${error.message}`);
        }
    }

    /**
     * Create necessary directories
     */
    async createDirectories() {
        console.log('📁 Creating directories...');
        
        const directories = [
            'test-results',
            'test-files',
            'test-audio-files'
        ];
        
        for (const dir of directories) {
            const dirPath = path.join(this.targetPath, dir);
            try {
                await fs.mkdir(dirPath, { recursive: true });
                console.log(`  ✅ Created: ${dir}`);
            } catch (error) {
                console.warn(`  ⚠️ Failed to create ${dir}: ${error.message}`);
            }
        }
    }

    /**
     * Generate example files
     */
    async generateExamples() {
        console.log('📝 Generating example files...');
        
        try {
            // Copy basic usage example
            const exampleSource = path.join(this.toolkitPath, 'examples', 'basic-usage.js');
            const exampleTarget = path.join(this.targetPath, 'test-example.js');
            
            const exampleContent = await fs.readFile(exampleSource, 'utf8');
            const customizedExample = this.customizeExample(exampleContent);
            
            await fs.writeFile(exampleTarget, customizedExample);
            console.log(`  ✅ Created: test-example.js`);
            
            // Create package.json scripts
            await this.addPackageScripts();
            
        } catch (error) {
            console.warn(`⚠️ Failed to generate examples: ${error.message}`);
        }
    }

    /**
     * Validate setup completion
     */
    async validateSetup() {
        console.log('✅ Validating setup...');
        
        try {
            // Check if config file exists
            const configPath = path.join(this.targetPath, 'ai-testing-config.js');
            await fs.access(configPath);
            
            // Check if directories exist
            const testResultsPath = path.join(this.targetPath, 'test-results');
            await fs.access(testResultsPath);
            
            console.log('✅ Setup validation passed');
            
        } catch (error) {
            throw new Error(`Setup validation failed: ${error.message}`);
        }
    }

    /**
     * Detect project type based on structure
     */
    async detectProjectType() {
        try {
            // Check for audio-related dependencies
            const packageJsonPath = path.join(this.targetPath, 'package.json');
            const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
            
            const dependencies = {
                ...packageJson.dependencies,
                ...packageJson.devDependencies
            };
            
            // Check for audio processing libraries
            if (dependencies['@domchristie/needles'] || dependencies['node-ffmpeg']) {
                return 'audio-analyzer';
            }
            
            // Check for simple audio player
            if (dependencies['howler'] || dependencies['tone']) {
                return 'simple-player';
            }
            
            // Default to general Electron app
            return 'electron-react';
            
        } catch (error) {
            return 'unknown';
        }
    }

    /**
     * Generate project-specific configuration
     */
    async generateProjectConfig(projectType) {
        const baseConfigPath = path.join(this.toolkitPath, 'config', 'project-config.js');
        const baseConfig = await fs.readFile(baseConfigPath, 'utf8');
        
        // Customize config based on project type
        let customConfig = baseConfig;
        
        // Add project-specific overrides
        customConfig += `\n\n// Project-specific overrides for ${projectType}\n`;
        customConfig += `const projectOverrides = {\n`;
        customConfig += `    projectType: '${projectType}',\n`;
        customConfig += `    detectedAt: '${new Date().toISOString()}'\n`;
        customConfig += `};\n\n`;
        customConfig += `module.exports = { ...module.exports, ...projectOverrides };\n`;
        
        return customConfig;
    }

    /**
     * Customize example file for project
     */
    customizeExample(exampleContent) {
        // Replace placeholder paths with actual project structure
        let customized = exampleContent;
        
        // Update app path
        customized = customized.replace(
            /const appPath = process\.argv\[2\] \|\| process\.cwd\(\);/g,
            `const appPath = '${this.targetPath}';`
        );
        
        // Add project-specific comments
        customized = `// Auto-generated test example for your project\n// Generated on: ${new Date().toISOString()}\n\n${customized}`;
        
        return customized;
    }

    /**
     * Add testing scripts to package.json
     */
    async addPackageScripts() {
        try {
            const packageJsonPath = path.join(this.targetPath, 'package.json');
            const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
            
            // Add testing scripts
            packageJson.scripts = packageJson.scripts || {};
            
            const newScripts = {
                'test:ai': 'node AI-Testing-Toolkit/run-all-tests.js',
                'test:health': 'node test-example.js . health',
                'test:analysis': 'node test-example.js . analysis',
                'test:ipc': 'node test-example.js . ipc',
                'test:ui': 'node test-example.js . ui',
                'test:complete': 'node test-example.js . complete'
            };
            
            // Only add scripts that don't already exist
            let added = 0;
            for (const [script, command] of Object.entries(newScripts)) {
                if (!packageJson.scripts[script]) {
                    packageJson.scripts[script] = command;
                    added++;
                }
            }
            
            if (added > 0) {
                await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
                console.log(`  ✅ Added ${added} test scripts to package.json`);
            }
            
        } catch (error) {
            console.warn(`  ⚠️ Failed to update package.json: ${error.message}`);
        }
    }

    /**
     * Run shell command
     */
    async runCommand(command, args, cwd = process.cwd()) {
        return new Promise((resolve, reject) => {
            const process = spawn(command, args, {
                cwd,
                stdio: 'pipe'
            });
            
            let output = '';
            let error = '';
            
            process.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            process.stderr.on('data', (data) => {
                error += data.toString();
            });
            
            process.on('close', (code) => {
                if (code === 0) {
                    resolve(output);
                } else {
                    reject(new Error(`Command failed with code ${code}: ${error}`));
                }
            });
        });
    }

    /**
     * Print usage instructions
     */
    printUsageInstructions() {
        console.log('🎯 Usage Instructions:');
        console.log('======================\n');
        
        console.log('📋 Available Commands:');
        console.log('  npm run test:ai        # Run complete test suite');
        console.log('  npm run test:health    # Quick health check');
        console.log('  npm run test:analysis  # Test file analysis');
        console.log('  npm run test:ipc       # Test IPC communication');
        console.log('  npm run test:ui        # Test UI interactions');
        console.log('  npm run test:complete  # Comprehensive test\n');
        
        console.log('📁 Generated Files:');
        console.log('  ai-testing-config.js   # Project configuration');
        console.log('  test-example.js        # Usage examples');
        console.log('  test-results/          # Test output directory');
        console.log('  test-files/            # Test input files');
        console.log('  test-audio-files/      # Generated audio files\n');
        
        console.log('🔧 Next Steps:');
        console.log('  1. Review ai-testing-config.js and customize for your app');
        console.log('  2. Run "npm run test:health" to verify basic functionality');
        console.log('  3. Add test files to test-files/ directory');
        console.log('  4. Run "npm run test:complete" for full validation\n');
        
        console.log('📚 Documentation:');
        console.log('  See AI-Testing-Toolkit/README.md for detailed usage guide\n');
    }
}

// CLI execution
if (require.main === module) {
    const targetPath = process.argv[2] || process.cwd();
    const setup = new ToolkitSetup(targetPath);
    
    setup.setup().catch(error => {
        console.error('💥 Setup failed:', error);
        process.exit(1);
    });
}

module.exports = { ToolkitSetup };
