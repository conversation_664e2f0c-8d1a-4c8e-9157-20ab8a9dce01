/**
 * 🤖 AI Agent Browser Controller
 * 
 * Playwright-based browser automation for testing React applications.
 * Provides high-level methods for UI interaction, state inspection,
 * and automated testing workflows.
 */

const { chromium } = require('playwright');

class BrowserController {
    constructor(options = {}) {
        this.options = {
            headless: false,
            slowMo: 100,
            timeout: 30000,
            viewport: { width: 1280, height: 720 },
            ...options
        };
        
        this.browser = null;
        this.page = null;
        this.consoleLogs = [];
        this.networkRequests = [];
    }

    /**
     * Initialize browser and page
     */
    async init() {
        console.log('🌐 Initializing browser...');
        
        this.browser = await chromium.launch({
            headless: this.options.headless,
            slowMo: this.options.slowMo
        });
        
        this.page = await this.browser.newPage({
            viewport: this.options.viewport
        });
        
        // Set up event listeners
        this.page.on('console', msg => {
            this.consoleLogs.push({
                type: msg.type(),
                text: msg.text(),
                timestamp: Date.now()
            });
        });
        
        this.page.on('request', request => {
            this.networkRequests.push({
                url: request.url(),
                method: request.method(),
                timestamp: Date.now()
            });
        });
        
        console.log('✅ Browser initialized');
    }

    /**
     * Navigate to URL
     */
    async navigate(url) {
        if (!this.page) await this.init();
        
        console.log(`🔗 Navigating to: ${url}`);
        await this.page.goto(url, { waitUntil: 'networkidle' });
        
        // Wait for React to load
        await this.page.waitForFunction(() => window.React !== undefined, { timeout: 10000 });
        console.log('✅ Page loaded and React ready');
    }

    /**
     * Click element by selector
     */
    async clickElement(selector, options = {}) {
        console.log(`👆 Clicking element: ${selector}`);
        await this.page.click(selector, {
            timeout: this.options.timeout,
            ...options
        });
    }

    /**
     * Type text into element
     */
    async typeText(selector, text, options = {}) {
        console.log(`⌨️ Typing into ${selector}: ${text}`);
        await this.page.fill(selector, text, {
            timeout: this.options.timeout,
            ...options
        });
    }

    /**
     * Upload files to input
     */
    async uploadFiles(selector, filePaths) {
        console.log(`📁 Uploading files to ${selector}:`, filePaths);
        await this.page.setInputFiles(selector, filePaths);
    }

    /**
     * Drag and drop files
     */
    async dragDropFiles(targetSelector, filePaths) {
        console.log(`🎯 Drag & drop files to ${targetSelector}:`, filePaths);
        
        // Create file list for drag event
        const files = filePaths.map(path => ({
            name: path.split('/').pop(),
            type: this.getMimeType(path),
            path: path
        }));
        
        // Simulate drag and drop
        await this.page.evaluate(({ selector, files }) => {
            const target = document.querySelector(selector);
            if (!target) throw new Error(`Target element ${selector} not found`);
            
            // Create drag event
            const dataTransfer = new DataTransfer();
            files.forEach(file => {
                const fileObj = new File([''], file.name, { type: file.type });
                Object.defineProperty(fileObj, 'path', { value: file.path });
                dataTransfer.items.add(fileObj);
            });
            
            // Dispatch drop event
            const dropEvent = new DragEvent('drop', {
                dataTransfer: dataTransfer,
                bubbles: true
            });
            
            target.dispatchEvent(dropEvent);
        }, { selector: targetSelector, files });
    }

    /**
     * Wait for element to appear
     */
    async waitForElement(selector, timeout = this.options.timeout) {
        console.log(`⏳ Waiting for element: ${selector}`);
        await this.page.waitForSelector(selector, { timeout });
    }

    /**
     * Wait for text to appear
     */
    async waitForText(text, timeout = this.options.timeout) {
        console.log(`⏳ Waiting for text: ${text}`);
        await this.page.waitForFunction(
            text => document.body.innerText.includes(text),
            text,
            { timeout }
        );
    }

    /**
     * Wait for text to disappear
     */
    async waitForTextToDisappear(text, timeout = this.options.timeout) {
        console.log(`⏳ Waiting for text to disappear: ${text}`);
        await this.page.waitForFunction(
            text => !document.body.innerText.includes(text),
            text,
            { timeout }
        );
    }

    /**
     * Get element text
     */
    async getElementText(selector) {
        return await this.page.textContent(selector);
    }

    /**
     * Get element attribute
     */
    async getElementAttribute(selector, attribute) {
        return await this.page.getAttribute(selector, attribute);
    }

    /**
     * Check if element exists
     */
    async elementExists(selector) {
        const element = await this.page.$(selector);
        return element !== null;
    }

    /**
     * Check if element is visible
     */
    async isElementVisible(selector) {
        return await this.page.isVisible(selector);
    }

    /**
     * Execute JavaScript in page context
     */
    async evaluate(script, ...args) {
        return await this.page.evaluate(script, ...args);
    }

    /**
     * Get console logs
     */
    getConsoleLogs(filter = null) {
        if (filter) {
            return this.consoleLogs.filter(log => 
                log.text.toLowerCase().includes(filter.toLowerCase()) ||
                log.type === filter
            );
        }
        return this.consoleLogs;
    }

    /**
     * Get network requests
     */
    getNetworkRequests(filter = null) {
        if (filter) {
            return this.networkRequests.filter(req => 
                req.url.includes(filter) || req.method === filter
            );
        }
        return this.networkRequests;
    }

    /**
     * Take screenshot
     */
    async screenshot(path = null, options = {}) {
        const screenshotOptions = {
            fullPage: true,
            ...options
        };
        
        if (path) {
            screenshotOptions.path = path;
        }
        
        return await this.page.screenshot(screenshotOptions);
    }

    /**
     * Wait for analysis to complete (app-specific)
     */
    async waitForAnalysisComplete(timeout = 30000) {
        console.log('⏳ Waiting for analysis to complete...');
        
        // Wait for "Analyzing..." to disappear
        try {
            await this.waitForTextToDisappear('Analyzing...', timeout);
            console.log('✅ Analysis completed');
            return true;
        } catch (error) {
            console.log('❌ Analysis did not complete within timeout');
            throw error;
        }
    }

    /**
     * Get analysis results from UI
     */
    async getAnalysisResults() {
        console.log('📊 Extracting analysis results from UI...');
        
        return await this.evaluate(() => {
            // Extract LUFS value
            const lufsElement = document.querySelector('[data-testid="lufs-value"]') ||
                               document.querySelector('*:contains("LUFS")');
            
            // Extract True Peak value
            const truePeakElement = document.querySelector('[data-testid="truepeak-value"]') ||
                                   document.querySelector('*:contains("dBTP")');
            
            // Extract gain applied
            const gainElement = document.querySelector('[data-testid="gain-value"]') ||
                               document.querySelector('*:contains("Gain")');
            
            return {
                lufs: lufsElement ? lufsElement.textContent : null,
                truePeak: truePeakElement ? truePeakElement.textContent : null,
                gain: gainElement ? gainElement.textContent : null,
                timestamp: Date.now()
            };
        });
    }

    /**
     * Get MIME type from file extension
     */
    getMimeType(filePath) {
        const ext = filePath.split('.').pop().toLowerCase();
        const mimeTypes = {
            'wav': 'audio/wav',
            'mp3': 'audio/mpeg',
            'flac': 'audio/flac',
            'aac': 'audio/aac',
            'ogg': 'audio/ogg',
            'm4a': 'audio/mp4'
        };
        return mimeTypes[ext] || 'application/octet-stream';
    }

    /**
     * Close browser
     */
    async close() {
        if (this.browser) {
            console.log('🔚 Closing browser...');
            await this.browser.close();
            this.browser = null;
            this.page = null;
        }
    }
}

module.exports = { BrowserController };
