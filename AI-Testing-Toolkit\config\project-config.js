/**
 * 🤖 AI Testing Toolkit - Project Configuration
 * 
 * Configure the toolkit for your specific Electron + React project.
 * Modify these settings to match your application structure.
 */

module.exports = {
    // Application paths
    paths: {
        // Root directory of your Electron app
        appRoot: './',
        
        // Main Electron process file
        electronMain: './main/main.js',
        
        // React development server URL
        reactUrl: 'http://localhost:3000',
        
        // Test files directory
        testFiles: './test-files/',
        
        // Output directory for test results
        output: './test-results/',
        
        // Generated test audio files
        generatedAudio: './test-audio-files/'
    },
    
    // Application-specific selectors
    selectors: {
        // File drop zone
        dropZone: '[data-testid="drop-zone"], .drop-zone, #drop-zone',
        
        // File list container
        fileList: '[data-testid="file-list"], .file-list, #file-list',
        
        // Analysis panel
        analysisPanel: '[data-testid="analysis-panel"], .analysis-panel, #analysis-panel',
        
        // Waveform canvas
        waveform: 'canvas, [data-testid="waveform"], .waveform',
        
        // Audio player controls
        playButton: '[data-testid="play-button"], .play-button, #play-button',
        pauseButton: '[data-testid="pause-button"], .pause-button, #pause-button',
        
        // LUFS display
        lufsValue: '[data-testid="lufs-value"], .lufs-value, #lufs-value',
        
        // True Peak display
        truePeakValue: '[data-testid="truepeak-value"], .truepeak-value, #truepeak-value',
        
        // Gain display
        gainValue: '[data-testid="gain-value"], .gain-value, #gain-value'
    },
    
    // Timeout configurations (in milliseconds)
    timeouts: {
        // Application startup
        appStartup: 30000,
        
        // Audio analysis completion
        analysis: 60000,
        
        // UI interactions
        uiInteraction: 5000,
        
        // File loading
        fileLoad: 10000,
        
        // IPC communication
        ipc: 15000,
        
        // Browser navigation
        navigation: 10000
    },
    
    // Audio testing configuration
    audio: {
        // Target LUFS for Spotify normalization
        targetLUFS: -14.0,
        
        // Maximum True Peak ceiling
        maxTruePeak: -1.0,
        
        // LUFS tolerance for validation
        lufsTolerance: 0.1,
        
        // True Peak tolerance
        truePeakTolerance: 0.1,
        
        // Supported audio formats
        supportedFormats: ['wav', 'flac', 'mp3', 'aac', 'ogg', 'm4a'],
        
        // Test file configurations
        testFiles: {
            // Short test file (for quick tests)
            short: {
                duration: 5,
                lufs: -14.0,
                truePeak: -1.0,
                format: 'wav'
            },
            
            // Standard test file
            standard: {
                duration: 10,
                lufs: -16.0,
                truePeak: -2.0,
                format: 'wav'
            },
            
            // Loud test file (needs attenuation)
            loud: {
                duration: 5,
                lufs: -8.0,
                truePeak: -0.1,
                format: 'wav'
            },
            
            // Quiet test file (needs gain)
            quiet: {
                duration: 5,
                lufs: -23.0,
                truePeak: -6.0,
                format: 'wav'
            }
        }
    },
    
    // IPC handler names (customize for your app)
    ipcHandlers: {
        // Required handlers
        required: [
            'analyze-file',
            'open-file-dialog',
            'get-settings'
        ],
        
        // Optional handlers
        optional: [
            'play-audio',
            'pause-audio',
            'seek-audio',
            'set-settings',
            'export-results'
        ],
        
        // Analysis-specific handlers
        analysis: [
            'analyze-file',
            'cancel-analysis',
            'get-analysis-progress'
        ]
    },
    
    // Expected IPC events
    ipcEvents: {
        // Analysis events
        analysis: [
            'analysis-progress',
            'analysis-complete',
            'analysis-error'
        ],
        
        // Audio playback events
        playback: [
            'audio-loaded',
            'audio-playing',
            'audio-paused',
            'audio-ended'
        ],
        
        // Error events
        errors: [
            'error',
            'file-error',
            'analysis-error'
        ]
    },
    
    // Browser configuration
    browser: {
        // Run in headless mode
        headless: false,
        
        // Slow down interactions for debugging
        slowMo: 100,
        
        // Browser viewport size
        viewport: {
            width: 1280,
            height: 720
        },
        
        // Enable dev tools
        devtools: true
    },
    
    // Test data validation
    validation: {
        // LUFS validation rules
        lufs: {
            // Minimum acceptable LUFS value
            min: -60,
            
            // Maximum acceptable LUFS value
            max: 0,
            
            // Tolerance for comparisons
            tolerance: 0.1
        },
        
        // True Peak validation rules
        truePeak: {
            // Minimum acceptable True Peak
            min: -60,
            
            // Maximum acceptable True Peak
            max: 3,
            
            // Tolerance for comparisons
            tolerance: 0.1
        },
        
        // File size validation
        fileSize: {
            // Minimum file size (bytes)
            min: 1024,
            
            // Maximum file size (bytes) - 100MB
            max: 100 * 1024 * 1024
        }
    },
    
    // Logging configuration
    logging: {
        // Enable debug logging
        debug: true,
        
        // Log levels: 'error', 'warn', 'info', 'debug'
        level: 'debug',
        
        // Save logs to file
        saveToFile: true,
        
        // Log file path
        logFile: './test-results/test.log'
    },
    
    // Performance monitoring
    performance: {
        // Monitor memory usage
        monitorMemory: true,
        
        // Monitor CPU usage
        monitorCPU: true,
        
        // Performance thresholds
        thresholds: {
            // Maximum memory usage (MB)
            maxMemory: 512,
            
            // Maximum analysis time (seconds)
            maxAnalysisTime: 30,
            
            // Maximum startup time (seconds)
            maxStartupTime: 15
        }
    },
    
    // Test environment
    environment: {
        // Node.js version requirement
        nodeVersion: '>=16.0.0',
        
        // Required environment variables
        requiredEnvVars: [],
        
        // Test data cleanup
        cleanup: {
            // Clean up generated files after tests
            cleanupAfterTests: true,
            
            // Keep test results
            keepResults: true,
            
            // Maximum age of test results (days)
            maxResultAge: 7
        }
    }
};

/**
 * Get configuration for specific project type
 */
function getConfigForProject(projectType) {
    const baseConfig = module.exports;
    
    switch (projectType) {
        case 'audio-analyzer':
            return {
                ...baseConfig,
                timeouts: {
                    ...baseConfig.timeouts,
                    analysis: 120000 // Longer analysis timeout
                }
            };
            
        case 'simple-player':
            return {
                ...baseConfig,
                ipcHandlers: {
                    required: ['play-audio', 'pause-audio', 'open-file-dialog']
                }
            };
            
        default:
            return baseConfig;
    }
}

module.exports.getConfigForProject = getConfigForProject;
