/**
 * Dependency Health Checker - Library and Version Scanner
 * Automatically scans and validates all project dependencies
 * Specialized for audio engineering applications with VST plugin support
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class DependencyHealthChecker {
    constructor(config = {}) {
        this.config = {
            projectRoot: config.projectRoot || './',
            outputDir: config.outputDir || './dependency-reports',
            packageFiles: config.packageFiles || [
                'package.json', 'package-lock.json', 'yarn.lock',
                'requirements.txt', 'Pipfile', 'Pipfile.lock',
                'Cargo.toml', 'Cargo.lock', 'go.mod', 'go.sum'
            ],
            audioSpecificDependencies: {
                javascript: [
                    'web-audio-api', 'tone', 'howler', 'wavesurfer.js', 'pizzicato',
                    'audio-context', 'standardized-audio-context', 'audio-buffer-utils',
                    'wav-encoder', 'wav-decoder', 'flac.js', 'mp3-parser'
                ],
                python: [
                    'librosa', 'soundfile', 'pydub', 'scipy', 'numpy',
                    'pyaudio', 'sounddevice', 'aubio', 'essentia', 'madmom'
                ],
                cpp: [
                    'juce', 'portaudio', 'rtaudio', 'fftw', 'eigen',
                    'vst3sdk', 'aap', 'clap'
                ]
            },
            vulnerabilityThresholds: {
                critical: 0, // No critical vulnerabilities allowed
                high: 2, // Max 2 high severity vulnerabilities
                medium: 10, // Max 10 medium severity vulnerabilities
                low: 50 // Max 50 low severity vulnerabilities
            },
            outdatedThresholds: {
                major: 0.2, // 20% of dependencies can be major versions behind
                minor: 0.5, // 50% of dependencies can be minor versions behind
                patch: 1.0 // All patch updates should be applied
            },
            ...config
        };

        this.dependencies = new Map();
        this.vulnerabilities = [];
        this.outdatedPackages = [];
        this.healthScore = 100;
        this.lastScan = null;
    }

    /**
     * Initialize the dependency health checker
     */
    async initialize() {
        try {
            await fs.mkdir(this.config.outputDir, { recursive: true });
            console.log('✅ DependencyHealthChecker initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ DependencyHealthChecker initialization failed:', error);
            return false;
        }
    }

    /**
     * Run comprehensive dependency health check
     */
    async runHealthCheck() {
        console.log('🔍 Starting dependency health check...');

        try {
            // Scan for package files
            const packageFiles = await this.scanForPackageFiles();

            // Parse dependencies from each package file
            for (const file of packageFiles) {
                await this.parseDependencies(file);
            }

            // Check for vulnerabilities
            await this.checkVulnerabilities();

            // Check for outdated packages
            await this.checkOutdatedPackages();

            // Analyze audio-specific dependencies
            await this.analyzeAudioDependencies();

            // Calculate health score
            this.calculateHealthScore();

            // Generate report
            const report = await this.generateHealthReport();

            this.lastScan = new Date().toISOString();
            console.log('✅ Dependency health check completed');

            return report;

        } catch (error) {
            console.error('❌ Dependency health check failed:', error);
            throw error;
        }
    }

    /**
     * Scan for package files in the project
     */
    async scanForPackageFiles() {
        const foundFiles = [];

        for (const fileName of this.config.packageFiles) {
            const filePath = path.join(this.config.projectRoot, fileName);
            try {
                await fs.access(filePath);
                foundFiles.push(filePath);
                console.log(`📦 Found package file: ${fileName}`);
            } catch (error) {
                // File doesn't exist, continue
            }
        }

        return foundFiles;
    }

    /**
     * Parse dependencies from package file
     */
    async parseDependencies(filePath) {
        const fileName = path.basename(filePath);

        try {
            const content = await fs.readFile(filePath, 'utf8');

            switch (fileName) {
                case 'package.json':
                    await this.parsePackageJson(content, filePath);
                    break;
                case 'requirements.txt':
                    await this.parseRequirementsTxt(content, filePath);
                    break;
                case 'Cargo.toml':
                    await this.parseCargoToml(content, filePath);
                    break;
                case 'go.mod':
                    await this.parseGoMod(content, filePath);
                    break;
                default:
                    console.log(`⚠️ Unsupported package file format: ${fileName}`);
            }
        } catch (error) {
            console.error(`❌ Failed to parse ${fileName}:`, error);
        }
    }

    /**
     * Parse package.json dependencies
     */
    async parsePackageJson(content, filePath) {
        try {
            const packageData = JSON.parse(content);
            const allDeps = {
                ...packageData.dependencies,
                ...packageData.devDependencies,
                ...packageData.peerDependencies
            };

            for (const [name, version] of Object.entries(allDeps)) {
                this.dependencies.set(name, {
                    name,
                    version: this.cleanVersion(version),
                    type: this.getDependencyType(name, packageData),
                    ecosystem: 'npm',
                    source: filePath,
                    isAudioRelated: this.isAudioRelatedDependency(name, 'javascript')
                });
            }

            console.log(`📦 Parsed ${Object.keys(allDeps).length} dependencies from package.json`);
        } catch (error) {
            console.error('❌ Failed to parse package.json:', error);
        }
    }

    /**
     * Check for vulnerabilities (simulated)
     */
    async checkVulnerabilities() {
        console.log('🔒 Checking for security vulnerabilities...');

        // In a real implementation, this would integrate with:
        // - npm audit
        // - Snyk
        // - GitHub Security Advisories
        // - OSV Database

        // Simulate vulnerability check
        const vulnerablePackages = Array.from(this.dependencies.values())
            .filter(() => Math.random() < 0.05); // 5% chance of vulnerability

        for (const pkg of vulnerablePackages) {
            const severity = this.getRandomSeverity();
            this.vulnerabilities.push({
                package: pkg.name,
                version: pkg.version,
                severity,
                description: `Simulated ${severity} vulnerability in ${pkg.name}`,
                cve: `CVE-2024-${Math.floor(Math.random() * 10000)}`,
                fixedIn: this.generateFixedVersion(pkg.version),
                ecosystem: pkg.ecosystem
            });
        }

        console.log(`🔒 Found ${this.vulnerabilities.length} vulnerabilities`);
    }

    /**
     * Check for outdated packages (simulated)
     */
    async checkOutdatedPackages() {
        console.log('📅 Checking for outdated packages...');

        // In a real implementation, this would check:
        // - npm outdated
        // - pip list --outdated
        // - cargo outdated
        // - go list -u -m all

        // Simulate outdated check
        const outdatedPackages = Array.from(this.dependencies.values())
            .filter(() => Math.random() < 0.3); // 30% chance of being outdated

        for (const pkg of outdatedPackages) {
            const updateType = this.getRandomUpdateType();
            this.outdatedPackages.push({
                package: pkg.name,
                currentVersion: pkg.version,
                latestVersion: this.generateNewerVersion(pkg.version, updateType),
                updateType,
                ecosystem: pkg.ecosystem,
                isAudioRelated: pkg.isAudioRelated
            });
        }

        console.log(`📅 Found ${this.outdatedPackages.length} outdated packages`);
    }

    /**
     * Analyze audio-specific dependencies
     */
    async analyzeAudioDependencies() {
        console.log('🎵 Analyzing audio-specific dependencies...');

        const audioDeps = Array.from(this.dependencies.values())
            .filter(dep => dep.isAudioRelated);

        const analysis = {
            totalAudioDependencies: audioDeps.length,
            byEcosystem: this.groupByEcosystem(audioDeps),
            vulnerableAudioDeps: this.vulnerabilities.filter(vuln =>
                audioDeps.some(dep => dep.name === vuln.package)
            ),
            outdatedAudioDeps: this.outdatedPackages.filter(outdated =>
                outdated.isAudioRelated
            ),
            recommendations: this.generateAudioRecommendations(audioDeps)
        };

        this.audioAnalysis = analysis;
        console.log(`🎵 Analyzed ${audioDeps.length} audio-specific dependencies`);
    }

    /**
     * Calculate overall health score
     */
    calculateHealthScore() {
        let score = 100;

        // Deduct points for vulnerabilities
        const vulnCounts = this.countVulnerabilitiesBySeverity();
        score -= vulnCounts.critical * 25; // Critical: -25 points each
        score -= vulnCounts.high * 10; // High: -10 points each
        score -= vulnCounts.medium * 3; // Medium: -3 points each
        score -= vulnCounts.low * 1; // Low: -1 point each

        // Deduct points for outdated packages
        const outdatedCounts = this.countOutdatedByType();
        score -= outdatedCounts.major * 5; // Major: -5 points each
        score -= outdatedCounts.minor * 2; // Minor: -2 points each
        score -= outdatedCounts.patch * 0.5; // Patch: -0.5 points each

        // Bonus points for audio-specific dependency health
        if (this.audioAnalysis) {
            const audioHealthBonus = this.calculateAudioHealthBonus();
            score += audioHealthBonus;
        }

        this.healthScore = Math.max(0, Math.min(100, score));
        console.log(`📊 Calculated health score: ${this.healthScore.toFixed(1)}/100`);
    }

    // Helper methods
    cleanVersion(version) {
        return version.replace(/[^0-9.]/g, '');
    }

    getDependencyType(name, packageData) {
        if (packageData.dependencies && packageData.dependencies[name]) return 'production';
        if (packageData.devDependencies && packageData.devDependencies[name]) return 'development';
        if (packageData.peerDependencies && packageData.peerDependencies[name]) return 'peer';
        return 'unknown';
    }

    isAudioRelatedDependency(name, ecosystem) {
        const audioDeps = this.config.audioSpecificDependencies[ecosystem] || [];
        return audioDeps.some(dep => name.toLowerCase().includes(dep.toLowerCase()));
    }

    getRandomSeverity() {
        const severities = ['low', 'medium', 'high', 'critical'];
        const weights = [0.5, 0.3, 0.15, 0.05]; // Most vulnerabilities are low/medium
        const random = Math.random();
        let cumulative = 0;

        for (let i = 0; i < severities.length; i++) {
            cumulative += weights[i];
            if (random < cumulative) return severities[i];
        }

        return 'low';
    }

    getRandomUpdateType() {
        const types = ['patch', 'minor', 'major'];
        const weights = [0.6, 0.3, 0.1]; // Most updates are patches
        const random = Math.random();
        let cumulative = 0;

        for (let i = 0; i < types.length; i++) {
            cumulative += weights[i];
            if (random < cumulative) return types[i];
        }

        return 'patch';
    }

    generateFixedVersion(currentVersion) {
        const parts = currentVersion.split('.');
        if (parts.length >= 3) {
            parts[2] = (parseInt(parts[2]) + 1).toString();
            return parts.join('.');
        }
        return currentVersion;
    }

    generateNewerVersion(currentVersion, updateType) {
        const parts = currentVersion.split('.').map(p => parseInt(p) || 0);

        switch (updateType) {
            case 'major':
                parts[0] += 1;
                parts[1] = 0;
                parts[2] = 0;
                break;
            case 'minor':
                parts[1] += 1;
                parts[2] = 0;
                break;
            case 'patch':
                parts[2] += 1;
                break;
        }

        return parts.join('.');
    }

    countVulnerabilitiesBySeverity() {
        const counts = { critical: 0, high: 0, medium: 0, low: 0 };
        this.vulnerabilities.forEach(vuln => {
            counts[vuln.severity] = (counts[vuln.severity] || 0) + 1;
        });
        return counts;
    }

    countOutdatedByType() {
        const counts = { major: 0, minor: 0, patch: 0 };
        this.outdatedPackages.forEach(pkg => {
            counts[pkg.updateType] = (counts[pkg.updateType] || 0) + 1;
        });
        return counts;
    }

    groupByEcosystem(deps) {
        const grouped = {};
        deps.forEach(dep => {
            grouped[dep.ecosystem] = (grouped[dep.ecosystem] || 0) + 1;
        });
        return grouped;
    }

    calculateAudioHealthBonus() {
        if (!this.audioAnalysis) return 0;

        const totalAudio = this.audioAnalysis.totalAudioDependencies;
        const vulnerableAudio = this.audioAnalysis.vulnerableAudioDeps.length;
        const outdatedAudio = this.audioAnalysis.outdatedAudioDeps.length;

        if (totalAudio === 0) return 0;

        const healthyAudioRatio = (totalAudio - vulnerableAudio - outdatedAudio) / totalAudio;
        return healthyAudioRatio * 5; // Up to 5 bonus points
    }

    generateAudioRecommendations(audioDeps) {
        const recommendations = [];

        if (audioDeps.length === 0) {
            recommendations.push({
                type: 'missing_audio_deps',
                priority: 'medium',
                message: 'No audio-specific dependencies detected.',
                action: 'Consider adding audio processing libraries if this is an audio application.'
            });
        }

        const vulnerableAudio = this.vulnerabilities.filter(vuln =>
            audioDeps.some(dep => dep.name === vuln.package)
        );

        if (vulnerableAudio.length > 0) {
            recommendations.push({
                type: 'vulnerable_audio_deps',
                priority: 'high',
                message: `${vulnerableAudio.length} audio dependencies have security vulnerabilities.`,
                action: 'Update vulnerable audio dependencies immediately to maintain security.'
            });
        }

        return recommendations;
    }

    /**
     * Generate comprehensive health report
     */
    async generateHealthReport() {
        const report = {
            timestamp: new Date().toISOString(),
            healthScore: this.healthScore,
            summary: {
                totalDependencies: this.dependencies.size,
                vulnerabilities: this.vulnerabilities.length,
                outdatedPackages: this.outdatedPackages.length,
                audioDependencies: (this.audioAnalysis && this.audioAnalysis.totalAudioDependencies) || 0
            },
            vulnerabilities: this.vulnerabilities,
            outdatedPackages: this.outdatedPackages,
            audioAnalysis: this.audioAnalysis,
            recommendations: this.generateOverallRecommendations(),
            ecosystemBreakdown: this.generateEcosystemBreakdown()
        };

        // Save report
        const reportPath = path.join(this.config.outputDir, `dependency-health-${Date.now()}.json`);
        await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

        return report;
    }

    generateOverallRecommendations() {
        const recommendations = [];

        const vulnCounts = this.countVulnerabilitiesBySeverity();
        if (vulnCounts.critical > 0) {
            recommendations.push({
                type: 'critical_vulnerabilities',
                priority: 'critical',
                message: `${vulnCounts.critical} critical vulnerabilities found.`,
                action: 'Update or replace affected packages immediately.'
            });
        }

        if (this.healthScore < 70) {
            recommendations.push({
                type: 'low_health_score',
                priority: 'high',
                message: 'Dependency health score is below acceptable threshold.',
                action: 'Address vulnerabilities and update outdated packages.'
            });
        }

        return recommendations;
    }

    generateEcosystemBreakdown() {
        const breakdown = {};

        for (const dep of this.dependencies.values()) {
            if (!breakdown[dep.ecosystem]) {
                breakdown[dep.ecosystem] = {
                    total: 0,
                    vulnerable: 0,
                    outdated: 0,
                    audioRelated: 0
                };
            }

            breakdown[dep.ecosystem].total++;
            if (dep.isAudioRelated) breakdown[dep.ecosystem].audioRelated++;
        }

        // Add vulnerability and outdated counts
        this.vulnerabilities.forEach(vuln => {
            if (breakdown[vuln.ecosystem]) {
                breakdown[vuln.ecosystem].vulnerable++;
            }
        });

        this.outdatedPackages.forEach(pkg => {
            if (breakdown[pkg.ecosystem]) {
                breakdown[pkg.ecosystem].outdated++;
            }
        });

        return breakdown;
    }
}

module.exports = { DependencyHealthChecker };