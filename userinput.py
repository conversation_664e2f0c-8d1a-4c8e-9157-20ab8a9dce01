# Fast and reliable userinput.py - Simple GUI with image support
import tkinter as tk
from tkinter import messagebox, scrolledtext
import time
import sys
import os

try:
    from PIL import Image, ImageGrab
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class FastInputGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("MasterRaccoon Development - Fast Feedback")
        self.root.geometry("700x500")
        self.root.configure(bg='#2d2d2d')
        
        # Center window
        self.center_window()
        
        self.user_input = ""
        self.clipboard_image = None
        self.timeout_seconds = 45
        self.timeout_id = None
        
        self.setup_ui()
        self.start_timeout()
        
    def center_window(self):
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (350)
        y = (self.root.winfo_screenheight() // 2) - (250)
        self.root.geometry(f"700x500+{x}+{y}")
        
    def setup_ui(self):
        # Main container
        main_frame = tk.Frame(self.root, bg='#2d2d2d', padx=15, pady=15)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(main_frame, 
                              text="🦝 MasterRaccoon Development", 
                              font=("Arial", 16, "bold"), 
                              fg='#4CAF50', bg='#2d2d2d')
        title_label.pack(pady=(0, 10))
        
        # Instructions
        pil_status = "✅ Text & Image paste: Ctrl+V" if PIL_AVAILABLE else "✅ Text paste: Ctrl+V"
        instructions = tk.Label(main_frame,
                               text=f"Share feedback below • {pil_status} • Independent timer",
                               font=("Arial", 10),
                               fg='#cccccc', bg='#2d2d2d')
        instructions.pack(pady=(0, 10))
        
        # Text input area
        self.text_area = scrolledtext.ScrolledText(main_frame,
                                                  height=18,
                                                  font=("Consolas", 11),
                                                  bg='#1e1e1e', fg='#ffffff',
                                                  insertbackground='#4CAF50',
                                                  selectbackground='#4CAF50',
                                                  wrap=tk.WORD,
                                                  undo=True,  # Enable undo/redo
                                                  maxundo=50)  # Limit undo stack
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.text_area.focus_set()
        
        # Bind events (no timer reset on typing)
        self.text_area.bind('<Control-v>', self.handle_paste)
        self.text_area.bind('<Control-V>', self.handle_paste)

        # Add proper text editing shortcuts
        self.text_area.bind('<Control-BackSpace>', self.delete_word_left)
        self.text_area.bind('<Control-Delete>', self.delete_word_right)
        self.text_area.bind('<Control-a>', self.select_all)
        self.text_area.bind('<Control-A>', self.select_all)
        self.text_area.bind('<Control-z>', self.undo)
        self.text_area.bind('<Control-Z>', self.undo)
        self.text_area.bind('<Control-y>', self.redo)
        self.text_area.bind('<Control-Y>', self.redo)
        
        # Bottom frame
        bottom_frame = tk.Frame(main_frame, bg='#2d2d2d')
        bottom_frame.pack(fill=tk.X)
        
        # Status label
        self.status_label = tk.Label(bottom_frame, 
                                    text=f"Ready • Independent timer: {self.timeout_seconds}s", 
                                    font=("Arial", 9), 
                                    fg='white', bg='#2d2d2d')
        self.status_label.pack(side=tk.LEFT)
        
        # Buttons
        button_frame = tk.Frame(bottom_frame, bg='#2d2d2d')
        button_frame.pack(side=tk.RIGHT)
        
        # Send button
        send_btn = tk.Button(button_frame, text="Send", 
                            command=self.send_feedback,
                            font=("Arial", 11, "bold"), 
                            bg='#4CAF50', fg='white',
                            padx=15, pady=5)
        send_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # Clear button
        clear_btn = tk.Button(button_frame, text="Clear", 
                             command=self.clear_all,
                             font=("Arial", 11), 
                             bg='#f44336', fg='white',
                             padx=15, pady=5)
        clear_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # Keyboard shortcuts
        self.root.bind('<Control-Return>', lambda e: self.send_feedback())
        self.root.bind('<Escape>', lambda e: self.root.quit())
        
    def handle_paste(self, event):
        """Handle both text and image pasting with Ctrl+V"""
        try:
            # First try to get text from clipboard
            try:
                clipboard_text = self.root.clipboard_get()
                if clipboard_text and clipboard_text.strip():
                    # Text found - let default paste behavior handle it
                    self.status_label.configure(text="✅ Text pasted", fg='lightgreen')
                    return  # Allow default text paste behavior
            except tk.TclError:
                # No text in clipboard, try image
                pass

            # Try image paste if no text or text paste failed
            if PIL_AVAILABLE:
                image = ImageGrab.grabclipboard()

                if image:
                    # Save image with timestamp
                    timestamp = int(time.time())
                    image_filename = f"clipboard_image_{timestamp}.png"
                    image.save(image_filename)

                    self.clipboard_image = image

                    # Add text indicator
                    current_text = self.text_area.get("1.0", tk.END).strip()
                    if current_text and not current_text.endswith("\n"):
                        current_text += "\n\n"
                    current_text += f"[IMAGE PASTED: {image_filename}]\n"
                    current_text += f"Size: {image.size[0]}x{image.size[1]} pixels\n"

                    self.text_area.delete("1.0", tk.END)
                    self.text_area.insert("1.0", current_text)

                    self.status_label.configure(text="✅ Image pasted", fg='lightgreen')
                    return "break"  # Prevent default behavior for image
                else:
                    self.status_label.configure(text="❌ No text or image in clipboard", fg='orange')
            else:
                self.status_label.configure(text="❌ No text in clipboard, PIL not available for images", fg='orange')

        except Exception as e:
            self.status_label.configure(text=f"❌ Paste error: {str(e)[:30]}", fg='red')

        return "break"
        
    def clear_all(self):
        self.text_area.delete("1.0", tk.END)
        self.clipboard_image = None
        self.text_area.focus_set()

    def delete_word_left(self, event):
        """Delete word to the left of cursor (Ctrl+Backspace)"""
        try:
            cursor_pos = self.text_area.index(tk.INSERT)
            line, col = map(int, cursor_pos.split('.'))

            # Get current line text up to cursor
            line_text = self.text_area.get(f"{line}.0", cursor_pos)

            # Find start of current word
            word_start = col
            while word_start > 0 and line_text[word_start - 1].isalnum():
                word_start -= 1

            # If we're at word boundary, find previous word
            if word_start == col:
                while word_start > 0 and not line_text[word_start - 1].isalnum():
                    word_start -= 1
                while word_start > 0 and line_text[word_start - 1].isalnum():
                    word_start -= 1

            # Delete from word start to cursor
            self.text_area.delete(f"{line}.{word_start}", cursor_pos)
        except:
            pass  # Fail silently
        return "break"

    def delete_word_right(self, event):
        """Delete word to the right of cursor (Ctrl+Delete)"""
        try:
            cursor_pos = self.text_area.index(tk.INSERT)
            line, col = map(int, cursor_pos.split('.'))

            # Get current line text from cursor
            line_end = self.text_area.index(f"{line}.end")
            line_text = self.text_area.get(cursor_pos, line_end)

            # Find end of current word
            word_end = 0
            while word_end < len(line_text) and line_text[word_end].isalnum():
                word_end += 1

            # If we're at word boundary, find next word
            if word_end == 0:
                while word_end < len(line_text) and not line_text[word_end].isalnum():
                    word_end += 1
                while word_end < len(line_text) and line_text[word_end].isalnum():
                    word_end += 1

            # Delete from cursor to word end
            self.text_area.delete(cursor_pos, f"{line}.{col + word_end}")
        except:
            pass  # Fail silently
        return "break"

    def select_all(self, event):
        """Select all text (Ctrl+A)"""
        try:
            self.text_area.tag_add(tk.SEL, "1.0", tk.END)
            self.text_area.mark_set(tk.INSERT, "1.0")
            self.text_area.see(tk.INSERT)
        except:
            pass  # Fail silently
        return "break"

    def undo(self, event):
        """Undo last action (Ctrl+Z)"""
        try:
            self.text_area.edit_undo()
        except:
            pass  # Fail silently
        return "break"

    def redo(self, event):
        """Redo last undone action (Ctrl+Y)"""
        try:
            self.text_area.edit_redo()
        except:
            pass  # Fail silently
        return "break"
        
    def send_feedback(self):
        text_input = self.text_area.get("1.0", tk.END).strip()

        if not text_input and not self.clipboard_image:
            messagebox.showwarning("Empty", "Please enter feedback or paste an image!")
            return

        # Check if user wants to loop again
        if "loop again" in text_input.lower():
            # User wants more time - signal for AI to restart
            print("🔄 LOOP_AGAIN_REQUEST")
            self.stop_timeout()
            self.root.destroy()
            return

        # Normal send - prepare output with completion code
        output = f"USER_INPUT: {text_input}" if text_input else "USER_INPUT: [No text]"

        if self.clipboard_image:
            output += f"\n[IMAGE AVAILABLE: Check for clipboard_image_*.png files]"

        # Print the result immediately
        print(output)

        # Check for images and make them visible to AI
        import glob
        image_files = glob.glob("clipboard_image_*.png")
        if image_files:
            # Sort by timestamp (newest first)
            image_files.sort(reverse=True)
            latest_image = image_files[0]
            print(f"\n📸 LATEST_IMAGE_FOR_AI: {latest_image}")
            print("🤖 AI can view this image using the 'view' tool")

        # Add completion code and close
        print("🤖 MESSAGE_COMPLETE_PROCEED")

        # For normal messages, close the GUI so AI can proceed
        self.stop_timeout()
        self.root.destroy()
        
    def start_timeout(self):
        # Start independent countdown
        self.countdown = self.timeout_seconds
        self.update_countdown()
        
    def update_countdown(self):
        if self.countdown > 0:
            # Set colors as requested: white -> orange (20s) -> red (10s)
            if self.countdown <= 10:
                color = 'red'
            elif self.countdown <= 20:
                color = 'orange'
            else:
                color = 'white'
                
            self.status_label.configure(
                text=f"Auto-submit in {self.countdown}s • Independent timer", 
                fg=color
            )
            
            self.countdown -= 1
            self.timeout_id = self.root.after(1000, self.update_countdown)
        else:
            # Timeout reached - signal for AI to restart
            print("🔄 LOOP_AGAIN_REQUEST")
            self.stop_timeout()
            self.root.destroy()
            
    def stop_timeout(self):
        if self.timeout_id:
            self.root.after_cancel(self.timeout_id)
            self.timeout_id = None
        
    # Removed run() method - GUI is now persistent

def main():
    """Single GUI session - managed by loop_manager.py"""
    try:
        gui = FastInputGUI()

        # Add a special binding to handle window close
        def on_closing():
            print("USER_INPUT: Window closed by user")
            print("🤖 MESSAGE_COMPLETE_PROCEED")
            gui.root.quit()

        gui.root.protocol("WM_DELETE_WINDOW", on_closing)

        # Run the GUI
        gui.root.mainloop()

    except KeyboardInterrupt:
        print("\nUSER_INPUT: Interrupted by user")
        print("🤖 MESSAGE_COMPLETE_PROCEED")
    except Exception as e:
        print(f"USER_INPUT: Critical Error: {str(e)}")
        print("🤖 MESSAGE_COMPLETE_PROCEED")
        # Try fallback
        try:
            user_input = input("Fallback prompt: ")
            print(f"USER_INPUT: {user_input}")
            print("🤖 MESSAGE_COMPLETE_PROCEED")
        except:
            print("USER_INPUT: Complete failure")
            print("🤖 MESSAGE_COMPLETE_PROCEED")

if __name__ == "__main__":
    main()

# Legacy compatibility
user_input = ""
