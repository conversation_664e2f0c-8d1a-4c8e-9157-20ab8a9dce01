/**
 * 🤖 AI Agent Workflow Tester
 * 
 * End-to-end workflow testing for complete application scenarios.
 * Orchestrates multiple tools to test complex user journeys
 * and validate complete functionality.
 */

const { ElectronTester } = require('../core-tools/electron-tester');
const { BrowserController } = require('../core-tools/browser-controller');
const { IPCTester } = require('../ipc-tools/ipc-tester');
const { FileDropSimulator } = require('../ui-tools/file-drop-simulator');
const { LUFSValidator } = require('../audio-tools/lufs-validator');

class WorkflowTester {
    constructor(appPath, options = {}) {
        this.appPath = appPath;
        this.options = {
            timeout: 60000,
            debug: true,
            headless: false,
            ...options
        };
        
        // Initialize tools
        this.electronTester = new ElectronTester(appPath, options);
        this.browserController = new BrowserController(options);
        this.ipcTester = new IPCTester(appPath, options);
        this.fileDropSimulator = new FileDropSimulator(options);
        this.lufsValidator = new LUFSValidator(options);
        
        this.testResults = [];
    }

    /**
     * Test complete file analysis workflow
     */
    async testFileAnalysisWorkflow(testFiles) {
        console.log('🔄 Testing complete file analysis workflow...');
        
        const workflow = {
            name: 'File Analysis Workflow',
            startTime: Date.now(),
            steps: [],
            success: false,
            errors: []
        };
        
        try {
            // Step 1: Start application
            workflow.steps.push(await this.stepStartApplication());
            
            // Step 2: Load UI
            workflow.steps.push(await this.stepLoadUI());
            
            // Step 3: Drop files
            workflow.steps.push(await this.stepDropFiles(testFiles));
            
            // Step 4: Wait for analysis
            workflow.steps.push(await this.stepWaitForAnalysis());
            
            // Step 5: Validate results
            workflow.steps.push(await this.stepValidateResults());
            
            // Step 6: Test UI updates
            workflow.steps.push(await this.stepTestUIUpdates());
            
            workflow.success = workflow.steps.every(step => step.success);
            workflow.endTime = Date.now();
            workflow.duration = workflow.endTime - workflow.startTime;
            
            console.log(`${workflow.success ? '✅' : '❌'} File analysis workflow completed`);
            
        } catch (error) {
            workflow.errors.push(error.message);
            workflow.success = false;
            console.error('❌ Workflow failed:', error);
        }
        
        this.testResults.push(workflow);
        return workflow;
    }

    /**
     * Test IPC communication workflow
     */
    async testIPCWorkflow() {
        console.log('🔄 Testing IPC communication workflow...');
        
        const workflow = {
            name: 'IPC Communication Workflow',
            startTime: Date.now(),
            steps: [],
            success: false,
            errors: []
        };
        
        try {
            // Step 1: Validate handlers exist
            workflow.steps.push(await this.stepValidateIPCHandlers());
            
            // Step 2: Test individual handlers
            workflow.steps.push(await this.stepTestIPCHandlers());
            
            // Step 3: Test event flow
            workflow.steps.push(await this.stepTestEventFlow());
            
            // Step 4: Test error handling
            workflow.steps.push(await this.stepTestErrorHandling());
            
            workflow.success = workflow.steps.every(step => step.success);
            workflow.endTime = Date.now();
            workflow.duration = workflow.endTime - workflow.startTime;
            
            console.log(`${workflow.success ? '✅' : '❌'} IPC workflow completed`);
            
        } catch (error) {
            workflow.errors.push(error.message);
            workflow.success = false;
            console.error('❌ IPC workflow failed:', error);
        }
        
        this.testResults.push(workflow);
        return workflow;
    }

    /**
     * Test audio processing workflow
     */
    async testAudioProcessingWorkflow(testFile) {
        console.log('🎵 Testing audio processing workflow...');
        
        const workflow = {
            name: 'Audio Processing Workflow',
            startTime: Date.now(),
            steps: [],
            success: false,
            errors: []
        };
        
        try {
            // Step 1: Test backend analysis
            workflow.steps.push(await this.stepTestBackendAnalysis(testFile));
            
            // Step 2: Test LUFS calculation
            workflow.steps.push(await this.stepTestLUFSCalculation(testFile));
            
            // Step 3: Test normalization
            workflow.steps.push(await this.stepTestNormalization(testFile));
            
            // Step 4: Test waveform generation
            workflow.steps.push(await this.stepTestWaveformGeneration(testFile));
            
            workflow.success = workflow.steps.every(step => step.success);
            workflow.endTime = Date.now();
            workflow.duration = workflow.endTime - workflow.startTime;
            
            console.log(`${workflow.success ? '✅' : '❌'} Audio processing workflow completed`);
            
        } catch (error) {
            workflow.errors.push(error.message);
            workflow.success = false;
            console.error('❌ Audio processing workflow failed:', error);
        }
        
        this.testResults.push(workflow);
        return workflow;
    }

    /**
     * Step: Start application
     */
    async stepStartApplication() {
        console.log('📱 Step: Starting application...');
        
        const step = {
            name: 'Start Application',
            startTime: Date.now(),
            success: false,
            data: {}
        };
        
        try {
            await this.electronTester.start();
            step.success = this.electronTester.isHealthy();
            step.data.healthy = step.success;
            
        } catch (error) {
            step.error = error.message;
        }
        
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime;
        
        return step;
    }

    /**
     * Step: Load UI
     */
    async stepLoadUI() {
        console.log('🌐 Step: Loading UI...');
        
        const step = {
            name: 'Load UI',
            startTime: Date.now(),
            success: false,
            data: {}
        };
        
        try {
            await this.browserController.navigate('http://localhost:3000');
            
            // Wait for app to be ready
            await this.browserController.waitForElement('body');
            
            // Check for React
            const hasReact = await this.browserController.evaluate(() => {
                return window.React !== undefined;
            });
            
            step.success = hasReact;
            step.data.hasReact = hasReact;
            
        } catch (error) {
            step.error = error.message;
        }
        
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime;
        
        return step;
    }

    /**
     * Step: Drop files
     */
    async stepDropFiles(testFiles) {
        console.log('📁 Step: Dropping files...');
        
        const step = {
            name: 'Drop Files',
            startTime: Date.now(),
            success: false,
            data: { files: testFiles }
        };
        
        try {
            // Find drop zone
            const dropZoneExists = await this.browserController.elementExists('[data-testid="drop-zone"]') ||
                                  await this.browserController.elementExists('.drop-zone') ||
                                  await this.browserController.elementExists('#drop-zone');
            
            if (!dropZoneExists) {
                throw new Error('Drop zone not found');
            }
            
            // Perform file drop
            const dropTarget = '[data-testid="drop-zone"]';
            await this.fileDropSimulator.dropFiles(testFiles, dropTarget, this.browserController.page);
            
            // Wait for files to be processed
            await this.browserController.wait(1000);
            
            step.success = true;
            
        } catch (error) {
            step.error = error.message;
        }
        
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime;
        
        return step;
    }

    /**
     * Step: Wait for analysis
     */
    async stepWaitForAnalysis() {
        console.log('⏳ Step: Waiting for analysis...');
        
        const step = {
            name: 'Wait for Analysis',
            startTime: Date.now(),
            success: false,
            data: {}
        };
        
        try {
            // Wait for analysis to complete
            await this.browserController.waitForAnalysisComplete(30000);
            
            step.success = true;
            
        } catch (error) {
            step.error = error.message;
            
            // Capture current state for debugging
            step.data.consoleLogs = this.browserController.getConsoleLogs();
            step.data.currentText = await this.browserController.evaluate(() => document.body.innerText);
        }
        
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime;
        
        return step;
    }

    /**
     * Step: Validate results
     */
    async stepValidateResults() {
        console.log('✅ Step: Validating results...');
        
        const step = {
            name: 'Validate Results',
            startTime: Date.now(),
            success: false,
            data: {}
        };
        
        try {
            // Extract results from UI
            const results = await this.browserController.getAnalysisResults();
            step.data.uiResults = results;
            
            // Validate LUFS values
            if (results.lufs) {
                const validation = await this.lufsValidator.validateLUFS('test-file', {
                    lufs: parseFloat(results.lufs.replace(/[^\d.-]/g, ''))
                });
                step.data.validation = validation;
                step.success = validation.valid;
            } else {
                step.error = 'No LUFS results found in UI';
            }
            
        } catch (error) {
            step.error = error.message;
        }
        
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime;
        
        return step;
    }

    /**
     * Step: Test UI updates
     */
    async stepTestUIUpdates() {
        console.log('🎨 Step: Testing UI updates...');
        
        const step = {
            name: 'Test UI Updates',
            startTime: Date.now(),
            success: false,
            data: {}
        };
        
        try {
            // Check for analysis panel
            const hasAnalysisPanel = await this.browserController.elementExists('[data-testid="analysis-panel"]') ||
                                    await this.browserController.elementExists('.analysis-panel');
            
            // Check for waveform
            const hasWaveform = await this.browserController.elementExists('canvas') ||
                               await this.browserController.elementExists('[data-testid="waveform"]');
            
            // Check for file list
            const hasFileList = await this.browserController.elementExists('[data-testid="file-list"]') ||
                               await this.browserController.elementExists('.file-list');
            
            step.data.hasAnalysisPanel = hasAnalysisPanel;
            step.data.hasWaveform = hasWaveform;
            step.data.hasFileList = hasFileList;
            
            step.success = hasAnalysisPanel || hasWaveform || hasFileList;
            
        } catch (error) {
            step.error = error.message;
        }
        
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime;
        
        return step;
    }

    /**
     * Step: Validate IPC handlers
     */
    async stepValidateIPCHandlers() {
        console.log('🔧 Step: Validating IPC handlers...');
        
        const step = {
            name: 'Validate IPC Handlers',
            startTime: Date.now(),
            success: false,
            data: {}
        };
        
        try {
            const requiredHandlers = [
                'analyze-file',
                'open-file-dialog',
                'get-settings'
            ];
            
            const result = await this.ipcTester.validateHandlers(requiredHandlers);
            step.data.handlers = result;
            step.success = result.missing.length === 0;
            
        } catch (error) {
            step.error = error.message;
        }
        
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime;
        
        return step;
    }

    /**
     * Step: Test IPC handlers
     */
    async stepTestIPCHandlers() {
        console.log('🔧 Step: Testing IPC handlers...');
        
        const step = {
            name: 'Test IPC Handlers',
            startTime: Date.now(),
            success: false,
            data: {}
        };
        
        try {
            // Test analyze-file handler
            const testFile = './TestMasters/AllIEverWanted_MasterA.wav';
            const result = await this.ipcTester.testHandler('analyze-file', testFile, { calculateWaveform: true });
            
            step.data.handlerResult = result;
            step.success = result.success && !result.error;
            
        } catch (error) {
            step.error = error.message;
        }
        
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime;
        
        return step;
    }

    /**
     * Step: Test event flow
     */
    async stepTestEventFlow() {
        console.log('📡 Step: Testing event flow...');
        
        const step = {
            name: 'Test Event Flow',
            startTime: Date.now(),
            success: false,
            data: {}
        };
        
        try {
            const testFile = './TestMasters/AllIEverWanted_MasterA.wav';
            const expectedEvents = ['analysis-progress', 'analysis-complete'];
            
            const result = await this.ipcTester.testIPCFlow('analyze-file', [testFile, { calculateWaveform: true }], expectedEvents);
            
            step.data.eventFlow = result;
            step.success = result.success && !result.error;
            
        } catch (error) {
            step.error = error.message;
        }
        
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime;
        
        return step;
    }

    /**
     * Step: Test error handling
     */
    async stepTestErrorHandling() {
        console.log('⚠️ Step: Testing error handling...');
        
        const step = {
            name: 'Test Error Handling',
            startTime: Date.now(),
            success: false,
            data: {}
        };
        
        try {
            // Test with invalid file
            const invalidFile = './nonexistent-file.wav';
            const result = await this.ipcTester.testHandler('analyze-file', invalidFile);
            
            // Should fail gracefully
            step.success = result.error !== undefined;
            step.data.errorHandling = result;
            
        } catch (error) {
            // Expected to fail
            step.success = true;
            step.data.expectedError = error.message;
        }
        
        step.endTime = Date.now();
        step.duration = step.endTime - step.startTime;
        
        return step;
    }

    /**
     * Generate comprehensive test report
     */
    generateReport() {
        console.log('📋 Generating workflow test report...');
        
        const report = {
            timestamp: Date.now(),
            totalWorkflows: this.testResults.length,
            passedWorkflows: this.testResults.filter(w => w.success).length,
            failedWorkflows: this.testResults.filter(w => !w.success).length,
            totalSteps: this.testResults.reduce((sum, w) => sum + w.steps.length, 0),
            passedSteps: this.testResults.reduce((sum, w) => sum + w.steps.filter(s => s.success).length, 0),
            workflows: this.testResults
        };
        
        report.workflowSuccessRate = report.totalWorkflows > 0 ? 
            (report.passedWorkflows / report.totalWorkflows * 100).toFixed(1) : 0;
        
        report.stepSuccessRate = report.totalSteps > 0 ? 
            (report.passedSteps / report.totalSteps * 100).toFixed(1) : 0;
        
        console.log(`📊 Workflow Test Report:`);
        console.log(`   Workflows: ${report.passedWorkflows}/${report.totalWorkflows} (${report.workflowSuccessRate}%)`);
        console.log(`   Steps: ${report.passedSteps}/${report.totalSteps} (${report.stepSuccessRate}%)`);
        
        return report;
    }

    /**
     * Cleanup resources
     */
    async cleanup() {
        console.log('🧹 Cleaning up workflow tester...');
        
        try {
            await this.browserController.close();
            await this.electronTester.stop();
        } catch (error) {
            console.warn('⚠️ Cleanup warning:', error.message);
        }
        
        console.log('✅ Cleanup completed');
    }
}

module.exports = { WorkflowTester };
