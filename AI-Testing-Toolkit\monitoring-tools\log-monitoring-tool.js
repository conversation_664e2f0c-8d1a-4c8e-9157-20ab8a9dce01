/**
 * Log Monitoring Tool - Universal Log Aggregator and Analy<PERSON>
 * Continuously monitors all logs and detects anomalies automatically
 * Specialized for audio engineering applications with real-time analysis
 */

const fs = require('fs').promises;
const path = require('path');
const { EventEmitter } = require('events');

class LogMonitoringTool extends EventEmitter {
    constructor(config = {}) {
        super();

        this.config = {
            logDir: config.logDir || './logs',
            outputDir: config.outputDir || './log-analysis',
            watchPatterns: config.watchPatterns || [
                '*.log', '*.txt', 'console.log', 'error.log', 'debug.log',
                'audio-processing.log', 'vst-plugins.log', 'performance.log'
            ],
            audioSpecificPatterns: {
                errors: [
                    /audio.*error/i, /vst.*error/i, /plugin.*error/i, /lufs.*error/i,
                    /sample.*rate.*error/i, /buffer.*underrun/i, /audio.*dropout/i,
                    /latency.*exceeded/i, /processing.*failed/i
                ],
                warnings: [
                    /audio.*warning/i, /vst.*warning/i, /plugin.*warning/i,
                    /high.*latency/i, /cpu.*usage.*high/i, /memory.*usage.*high/i,
                    /buffer.*size.*warning/i, /sample.*rate.*mismatch/i
                ],
                performance: [
                    /processing.*time/i, /render.*time/i, /load.*time/i,
                    /cpu.*usage/i, /memory.*usage/i, /latency/i, /fps/i,
                    /analysis.*duration/i, /vst.*load.*time/i
                ],
                success: [
                    /analysis.*complete/i, /file.*loaded/i, /vst.*loaded/i,
                    /processing.*complete/i, /export.*complete/i, /normalization.*complete/i
                ]
            },
            anomalyThresholds: {
                errorRate: 0.05, // 5% error rate threshold
                warningRate: 0.15, // 15% warning rate threshold
                performanceDegradation: 0.3, // 30% performance degradation
                logVolumeIncrease: 2.0, // 2x log volume increase
                responseTimeIncrease: 1.5 // 1.5x response time increase
            },
            realTimeAnalysis: config.realTimeAnalysis !== false,
            retentionDays: config.retentionDays || 30,
            ...config
        };

        this.logSources = new Map();
        this.logHistory = [];
        this.anomalies = [];
        this.metrics = {
            totalLogs: 0,
            errorCount: 0,
            warningCount: 0,
            performanceIssues: 0,
            lastAnalysis: null
        };

        this.watchers = new Map();
        this.isMonitoring = false;
    }

    /**
     * Initialize the log monitoring system
     */
    async initialize() {
        try {
            await fs.mkdir(this.config.logDir, { recursive: true });
            await fs.mkdir(this.config.outputDir, { recursive: true });

            // Load historical data
            await this.loadHistoricalData();

            // Start monitoring if real-time analysis is enabled
            if (this.config.realTimeAnalysis) {
                await this.startRealTimeMonitoring();
            }

            console.log('✅ LogMonitoringTool initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ LogMonitoringTool initialization failed:', error);
            return false;
        }
    }

    /**
     * Start real-time log monitoring
     */
    async startRealTimeMonitoring() {
        if (this.isMonitoring) {
            console.log('⚠️ Log monitoring already active');
            return;
        }

        console.log('🔍 Starting real-time log monitoring...');
        this.isMonitoring = true;

        // Monitor console output
        this.monitorConsoleOutput();

        // Monitor log files
        await this.monitorLogFiles();

        // Monitor browser console (if available)
        this.monitorBrowserConsole();

        // Start periodic analysis
        this.startPeriodicAnalysis();

        console.log('✅ Real-time log monitoring started');
    }

    /**
     * Process individual log entry
     */
    processLogEntry(source, level, message) {
        const entry = {
            timestamp: new Date().toISOString(),
            source,
            level,
            message,
            category: this.categorizeLogEntry(message),
            severity: this.calculateSeverity(level, message)
        };

        this.logHistory.push(entry);
        this.metrics.totalLogs++;

        // Update metrics
        if (level === 'error') this.metrics.errorCount++;
        if (level === 'warning') this.metrics.warningCount++;

        // Check for anomalies
        this.checkForAnomalies(entry);

        // Emit event for real-time processing
        this.emit('logEntry', entry);

        // Check if this is a critical issue
        if (entry.severity === 'critical') {
            this.emit('criticalIssue', entry);
        }
    }

    /**
     * Categorize log entry based on audio-specific patterns
     */
    categorizeLogEntry(message) {
        for (const [category, patterns] of Object.entries(this.config.audioSpecificPatterns)) {
            for (const pattern of patterns) {
                if (pattern.test(message)) {
                    return category;
                }
            }
        }
        return 'general';
    }

    /**
     * Calculate severity of log entry
     */
    calculateSeverity(level, message) {
        // Critical audio-specific issues
        const criticalPatterns = [
            /audio.*crash/i, /vst.*crash/i, /fatal.*error/i,
            /memory.*leak/i, /buffer.*overflow/i, /segmentation.*fault/i
        ];

        for (const pattern of criticalPatterns) {
            if (pattern.test(message)) {
                return 'critical';
            }
        }

        // High severity issues
        const highSeverityPatterns = [
            /audio.*dropout/i, /processing.*failed/i, /vst.*error/i,
            /latency.*exceeded/i, /buffer.*underrun/i
        ];

        for (const pattern of highSeverityPatterns) {
            if (pattern.test(message)) {
                return 'high';
            }
        }

        // Map log levels to severity
        const severityMap = {
            'error': 'high',
            'warning': 'medium',
            'info': 'low',
            'debug': 'low'
        };

        return severityMap[level] || 'low';
    }

    /**
     * Check for anomalies in log patterns
     */
    checkForAnomalies(entry) {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

        // Get recent logs for analysis
        const recentLogs = this.logHistory.filter(log =>
            new Date(log.timestamp) > oneHourAgo
        );

        // Check error rate
        const errorRate = recentLogs.filter(log => log.level === 'error').length / recentLogs.length;
        if (errorRate > this.config.anomalyThresholds.errorRate) {
            this.reportAnomaly('high_error_rate', {
                rate: errorRate,
                threshold: this.config.anomalyThresholds.errorRate,
                recentErrors: recentLogs.filter(log => log.level === 'error').slice(-5)
            });
        }

        // Check warning rate
        const warningRate = recentLogs.filter(log => log.level === 'warning').length / recentLogs.length;
        if (warningRate > this.config.anomalyThresholds.warningRate) {
            this.reportAnomaly('high_warning_rate', {
                rate: warningRate,
                threshold: this.config.anomalyThresholds.warningRate
            });
        }

        // Check for repeated error patterns
        this.checkRepeatedPatterns(recentLogs);

        // Check for performance degradation patterns
        this.checkPerformanceDegradation(recentLogs);
    }

    /**
     * Check for repeated error patterns
     */
    checkRepeatedPatterns(recentLogs) {
        const errorMessages = recentLogs
            .filter(log => log.level === 'error')
            .map(log => log.message);

        const messageCount = {};
        errorMessages.forEach(msg => {
            const key = msg.substring(0, 100); // First 100 chars as key
            messageCount[key] = (messageCount[key] || 0) + 1;
        });

        for (const [message, count] of Object.entries(messageCount)) {
            if (count >= 5) { // 5 or more identical errors
                this.reportAnomaly('repeated_error', {
                    message,
                    count,
                    timeframe: '1 hour'
                });
            }
        }
    }

    /**
     * Check for performance degradation
     */
    checkPerformanceDegradation(recentLogs) {
        const performanceLogs = recentLogs.filter(log =>
            log.category === 'performance'
        );

        // Extract timing information
        const timings = performanceLogs.map(log => {
            const match = log.message.match(/(\d+(?:\.\d+)?)\s*(ms|seconds?)/i);
            if (match) {
                const value = parseFloat(match[1]);
                const unit = match[2].toLowerCase();
                return unit.startsWith('s') ? value * 1000 : value; // Convert to ms
            }
            return null;
        }).filter(t => t !== null);

        if (timings.length >= 5) {
            const avgTiming = timings.reduce((a, b) => a + b, 0) / timings.length;
            const recentAvg = timings.slice(-5).reduce((a, b) => a + b, 0) / 5;

            if (recentAvg > avgTiming * this.config.anomalyThresholds.performanceDegradation) {
                this.reportAnomaly('performance_degradation', {
                    averageTiming: avgTiming,
                    recentAverage: recentAvg,
                    degradation: (recentAvg / avgTiming - 1) * 100
                });
            }
        }
    }

    /**
     * Report anomaly
     */
    reportAnomaly(type, data) {
        const anomaly = {
            id: crypto.randomUUID(),
            type,
            timestamp: new Date().toISOString(),
            data,
            severity: this.getAnomalySeverity(type),
            resolved: false
        };

        this.anomalies.push(anomaly);
        this.emit('anomaly', anomaly);

        console.warn(`🚨 Anomaly detected: ${type}`, data);
    }

    /**
     * Get anomaly severity
     */
    getAnomalySeverity(type) {
        const severityMap = {
            'high_error_rate': 'high',
            'high_warning_rate': 'medium',
            'repeated_error': 'high',
            'performance_degradation': 'medium',
            'log_volume_spike': 'low',
            'critical_pattern': 'critical'
        };

        return severityMap[type] || 'medium';
    }

    /**
     * Generate comprehensive log analysis report
     */
    async generateAnalysisReport() {
        const report = {
            timestamp: new Date().toISOString(),
            timeframe: {
                start: this.logHistory.length > 0 ? this.logHistory[0].timestamp : null,
                end: this.logHistory.length > 0 ? this.logHistory[this.logHistory.length - 1].timestamp : null
            },
            metrics: {...this.metrics },
            summary: this.generateSummary(),
            anomalies: this.anomalies.filter(a => !a.resolved),
            recommendations: this.generateRecommendations(),
            audioSpecificAnalysis: this.generateAudioSpecificAnalysis(),
            trends: this.analyzeTrends()
        };

        // Save report
        const reportPath = path.join(this.config.outputDir, `log-analysis-${Date.now()}.json`);
        await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

        return report;
    }

    /**
     * Generate summary
     */
    generateSummary() {
        const total = this.logHistory.length;
        const errors = this.metrics.errorCount;
        const warnings = this.metrics.warningCount;

        return {
            totalLogs: total,
            errorRate: total > 0 ? (errors / total * 100).toFixed(2) + '%' : '0%',
            warningRate: total > 0 ? (warnings / total * 100).toFixed(2) + '%' : '0%',
            healthScore: this.calculateHealthScore(),
            status: this.getOverallStatus()
        };
    }

    /**
     * Calculate health score
     */
    calculateHealthScore() {
        if (this.logHistory.length === 0) return 100;

        const errorRate = this.metrics.errorCount / this.logHistory.length;
        const warningRate = this.metrics.warningCount / this.logHistory.length;
        const anomalyCount = this.anomalies.filter(a => !a.resolved).length;

        let score = 100;
        score -= errorRate * 500; // Errors heavily impact score
        score -= warningRate * 200; // Warnings moderately impact score
        score -= anomalyCount * 10; // Each unresolved anomaly reduces score

        return Math.max(0, Math.min(100, score));
    }

    /**
     * Get overall status
     */
    getOverallStatus() {
        const healthScore = this.calculateHealthScore();
        const criticalAnomalies = this.anomalies.filter(a =>
            !a.resolved && a.severity === 'critical'
        ).length;

        if (criticalAnomalies > 0) return 'critical';
        if (healthScore < 50) return 'poor';
        if (healthScore < 80) return 'fair';
        return 'good';
    }

    /**
     * Generate recommendations
     */
    generateRecommendations() {
        const recommendations = [];

        // Error rate recommendations
        const errorRate = this.metrics.errorCount / this.logHistory.length;
        if (errorRate > 0.05) {
            recommendations.push({
                type: 'error_rate',
                priority: 'high',
                message: 'High error rate detected. Review error patterns and implement fixes.',
                action: 'Investigate most frequent error messages and their root causes.'
            });
        }

        // Performance recommendations
        const perfIssues = this.anomalies.filter(a =>
            a.type === 'performance_degradation' && !a.resolved
        );
        if (perfIssues.length > 0) {
            recommendations.push({
                type: 'performance',
                priority: 'medium',
                message: 'Performance degradation detected in audio processing.',
                action: 'Optimize audio processing algorithms and check system resources.'
            });
        }

        // VST plugin recommendations
        const vstErrors = this.logHistory.filter(log =>
            log.message.toLowerCase().includes('vst') && log.level === 'error'
        );
        if (vstErrors.length > 5) {
            recommendations.push({
                type: 'vst_stability',
                priority: 'high',
                message: 'Multiple VST plugin errors detected.',
                action: 'Review VST plugin compatibility and implement better error handling.'
            });
        }

        return recommendations;
    }

    /**
     * Generate audio-specific analysis
     */
    generateAudioSpecificAnalysis() {
        const audioLogs = this.logHistory.filter(log => ['errors', 'warnings', 'performance', 'success'].includes(log.category));

        const analysis = {
            audioProcessingErrors: audioLogs.filter(log =>
                log.category === 'errors' && /processing/i.test(log.message)
            ).length,
            vstPluginIssues: audioLogs.filter(log =>
                /vst|plugin/i.test(log.message) && log.level !== 'info'
            ).length,
            latencyIssues: audioLogs.filter(log =>
                /latency|delay/i.test(log.message)
            ).length,
            successfulOperations: audioLogs.filter(log =>
                log.category === 'success'
            ).length
        };

        return analysis;
    }

    /**
     * Analyze trends
     */
    analyzeTrends() {
        // Simple trend analysis over time
        const hourlyStats = {};

        this.logHistory.forEach(log => {
            const hour = new Date(log.timestamp).getHours();
            if (!hourlyStats[hour]) {
                hourlyStats[hour] = { total: 0, errors: 0, warnings: 0 };
            }
            hourlyStats[hour].total++;
            if (log.level === 'error') hourlyStats[hour].errors++;
            if (log.level === 'warning') hourlyStats[hour].warnings++;
        });

        return {
            hourlyDistribution: hourlyStats,
            peakErrorHour: this.findPeakHour(hourlyStats, 'errors'),
            peakActivityHour: this.findPeakHour(hourlyStats, 'total')
        };
    }

    /**
     * Find peak hour for a metric
     */
    findPeakHour(stats, metric) {
        let maxHour = 0;
        let maxValue = 0;

        for (const [hour, data] of Object.entries(stats)) {
            if (data[metric] > maxValue) {
                maxValue = data[metric];
                maxHour = parseInt(hour);
            }
        }

        return { hour: maxHour, value: maxValue };
    }

    // Helper methods
    matchesWatchPattern(filename) {
        return this.config.watchPatterns.some(pattern => {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(filename);
        });
    }

    detectLogLevel(line) {
        if (/error|fail|exception|crash/i.test(line)) return 'error';
        if (/warn|warning/i.test(line)) return 'warning';
        if (/info|information/i.test(line)) return 'info';
        if (/debug|trace/i.test(line)) return 'debug';
        return 'info';
    }

    startPeriodicAnalysis() {
        setInterval(async() => {
            this.metrics.lastAnalysis = new Date().toISOString();
            await this.generateAnalysisReport();
        }, 300000); // Every 5 minutes
    }

    async loadHistoricalData() {
        try {
            const files = await fs.readdir(this.config.outputDir);
            const reportFiles = files.filter(f => f.startsWith('log-analysis-'));
            console.log(`📊 Found ${reportFiles.length} historical log analysis reports`);
        } catch (error) {
            console.log('📊 No historical log data found, starting fresh');
        }
    }

    /**
     * Stop monitoring and cleanup
     */
    async stopMonitoring() {
        this.isMonitoring = false;

        // Close file watchers
        for (const watcher of this.watchers.values()) {
            watcher.close();
        }
        this.watchers.clear();

        // Generate final report
        await this.generateAnalysisReport();

        console.log('🛑 Log monitoring stopped');
    }
}

module.exports = { LogMonitoringTool };